#!/usr/bin/env python3
"""
Start a simple Gitea MCP server that implements the MCP protocol.
This server will connect to the Gitea API and provide MCP-compatible endpoints.
"""

import os
import sys
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from aiohttp import web, ClientSession
import aiohttp

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # Set to DEBUG for more detailed logs
    format="%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# Global variables
GITEA_URL = os.environ.get("GITEA_URL", "http://localhost:3000")
GITEA_ACCESS_TOKEN = os.environ.get("GITEA_ACCESS_TOKEN", "a5d21cbad37313f5aa95af91d385ea8917eb3d56")
GITEA_USERNAME = os.environ.get("GITEA_USERNAME", "Admin")
GITEA_PASSWORD = os.environ.get("GITEA_PASSWORD", "P@ssw0rd1")
SERVER_PORT = int(os.environ.get("GITMCP_SERVER_PORT", "8080"))

# Active sessions
active_sessions = {}

class GiteaMCPServer:
    """
    A simple Gitea MCP server that implements the MCP protocol.
    """
    def __init__(self):
        """Initialize the server."""
        self.app = web.Application()
        self.setup_routes()

        # Validate essential environment variables
        if not GITEA_URL:
            logger.error("GITEA_URL environment variable not set.")
            sys.exit(1)

        # Check authentication methods
        if not GITEA_ACCESS_TOKEN and not (GITEA_USERNAME and GITEA_PASSWORD):
            logger.warning("Neither GITEA_ACCESS_TOKEN nor GITEA_USERNAME/PASSWORD are set. Assuming anonymous access if possible.")
        elif GITEA_ACCESS_TOKEN:
            logger.info("Using Gitea access token for authentication.")
        else:
            logger.info("Using Gitea username/password for authentication.")

    def setup_routes(self):
        """Set up the routes for the server."""
        self.app.router.add_get("/health", self.handle_health)
        self.app.router.add_get("/version", self.handle_version)
        self.app.router.add_get("/sse", self.handle_sse)
        self.app.router.add_post("/sse", self.handle_sse)  # Add POST handler for SSE for streamable HTTP
        self.app.router.add_post("/message", self.handle_message)

        # Add a root handler for streamable HTTP
        self.app.router.add_post("/", self.handle_streamable_http)

        # Add middleware to log all requests
        @web.middleware
        async def log_request_middleware(request, handler):
            logger.info(f"Received request: {request.method} {request.path} from {request.remote}")
            logger.debug(f"Request headers: {request.headers}")
            try:
                response = await handler(request)
                logger.info(f"Response status: {response.status}")
                return response
            except web.HTTPException as ex:
                logger.error(f"HTTP exception: {ex.status} - {ex.reason}")
                raise
            except Exception as e:
                logger.error(f"Error handling request: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                raise

        self.app.middlewares.append(log_request_middleware)

    async def handle_health(self, request):
        """Handle health check requests."""
        return web.json_response({"status": "ok"})

    async def handle_version(self, request):
        """Handle version requests."""
        return web.json_response({
            "version": "0.1.0",
            "name": "Gitea MCP Server",
            "protocol_version": "2023-12-01"  # Use the protocol version expected by langchain-mcp-adapters
        })

    async def handle_sse(self, request):
        """Handle SSE connection requests."""
        if request.method == "GET":
            # Handle GET request for SSE connection establishment
            # Generate a unique session ID
            session_id = f"session_{len(active_sessions) + 1}"
            logger.debug(f"Creating new SSE session with ID: {session_id}")

            # Create a new response
            response = web.StreamResponse()
            response.headers["Content-Type"] = "text/event-stream"
            response.headers["Cache-Control"] = "no-cache"
            response.headers["Connection"] = "keep-alive"

            try:
                logger.debug(f"Preparing SSE response for session {session_id}")
                await response.prepare(request)
                logger.debug(f"SSE response prepared for session {session_id}")

                # Send the session ID
                message_url = f"{request.url.scheme}://{request.url.host}:{request.url.port}/message?sessionId={session_id}"
                logger.debug(f"Message URL for session {session_id}: {message_url}")

                await response.write(b"event: endpoint\n")
                await response.write(f"data: {message_url}\n\n".encode())
                logger.debug(f"Sent endpoint event for session {session_id}")

                # Store the session
                tools = self.get_tools()
                logger.debug(f"Created {len(tools)} tools for session {session_id}")

                active_sessions[session_id] = {
                    "response": response,
                    "initialized": False,
                    "tools": tools,
                    "created_at": asyncio.get_event_loop().time()
                }
                logger.info(f"Session {session_id} created and stored. Total active sessions: {len(active_sessions)}")

                # Keep the connection open
                try:
                    while True:
                        await asyncio.sleep(1)
                except asyncio.CancelledError:
                    # Client disconnected
                    logger.info(f"Client disconnected for session {session_id}")
                    if session_id in active_sessions:
                        del active_sessions[session_id]
                        logger.info(f"Session {session_id} removed. Remaining active sessions: {len(active_sessions)}")
                    raise

            except Exception as e:
                logger.error(f"Error in SSE handler for session {session_id}: {str(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                if session_id in active_sessions:
                    del active_sessions[session_id]
                    logger.info(f"Session {session_id} removed due to error. Remaining active sessions: {len(active_sessions)}")
                raise

        elif request.method == "POST":
            # Handle POST request - this is likely the LangChain adapter trying to send messages
            logger.info("Received POST request to /sse - redirecting to streamable HTTP handler")
            return await self.handle_streamable_http(request)

        else:
            logger.error(f"Unsupported method {request.method} for /sse endpoint")
            return web.json_response({"error": f"Method {request.method} not allowed"}, status=405)

    async def handle_message(self, request):
        """Handle message requests."""
        # Get the session ID
        session_id = request.query.get("sessionId")
        logger.debug(f"Received message request for session ID: {session_id}")

        if not session_id:
            logger.warning("Message request missing sessionId parameter")
            return web.json_response({"error": "Missing session ID"}, status=400)

        if session_id not in active_sessions:
            logger.warning(f"Message request for unknown session ID: {session_id}")
            logger.debug(f"Active sessions: {list(active_sessions.keys())}")
            return web.json_response({"error": "Invalid session ID"}, status=400)

        # Get the session
        session = active_sessions[session_id]
        session_age = asyncio.get_event_loop().time() - session.get("created_at", 0)
        logger.debug(f"Session {session_id} found. Age: {session_age:.2f}s, Initialized: {session['initialized']}")

        # Get the request body
        try:
            body = await request.json()
            logger.debug(f"Parsed JSON request body: {json.dumps(body)}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON request body: {str(e)}")
            return web.json_response({"error": "Invalid JSON"}, status=400)

        # Log the request for debugging
        method = body.get("method", "unknown")
        request_id = body.get("id", "unknown")
        logger.info(f"Received request: method={method}, id={request_id}, session={session_id}")

        # Handle the request
        if body.get("method") == "initialize":
            # Handle initialize request
            logger.info(f"Initializing session {session_id}")
            session["initialized"] = True

            # Send response according to MCP protocol
            response = {
                "jsonrpc": "2.0",
                "id": body.get("id"),
                "result": {
                    "capabilities": {
                        "sampling": {},
                        "roots": {
                            "listChanged": True
                        }
                    },
                    "serverInfo": {
                        "name": "Gitea MCP Server",
                        "version": "0.1.0",
                        "protocolVersion": "2023-12-01"  # Use the protocol version expected by langchain-mcp-adapters
                    }
                }
            }
            logger.debug(f"Sending initialize response: {json.dumps(response)}")
            return web.json_response(response)

        elif body.get("method") == "notifications/initialized":
            # Handle initialized notification
            logger.info(f"Received initialized notification for session {session_id}")
            return web.json_response({})

        elif body.get("method") == "session/terminate":
            # Handle session termination request
            logger.info(f"Terminating session: {session_id}")

            try:
                # Send a proper response before removing the session
                response = {
                    "jsonrpc": "2.0",
                    "id": body.get("id"),
                    "result": {}  # Empty result for successful termination
                }

                logger.debug(f"Sending termination response: {json.dumps(response)}")

                # Remove the session after preparing the response
                if session_id in active_sessions:
                    logger.debug(f"Removing session {session_id} from active_sessions")
                    del active_sessions[session_id]
                    logger.info(f"Session {session_id} removed. Remaining active sessions: {len(active_sessions)}")

                return web.json_response(response)
            except Exception as e:
                logger.error(f"Error terminating session {session_id}: {str(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return web.json_response({
                    "jsonrpc": "2.0",
                    "id": body.get("id"),
                    "error": {
                        "code": -32000,
                        "message": f"Error terminating session: {str(e)}"
                    }
                }, status=500)
        elif body.get("method") == "tools/list":
            # Handle tools list request
            if not session["initialized"]:
                return web.json_response({
                    "jsonrpc": "2.0",
                    "id": body.get("id"),
                    "error": {
                        "code": -32803,
                        "message": "Session not initialized"
                    }
                }, status=400)

            response = {
                "jsonrpc": "2.0",
                "id": body.get("id"),
                "result": {
                    "tools": session["tools"]
                }
            }
            logger.info(f"Sending tools/list response: {response}")
            return web.json_response(response)

        elif body.get("method") == "tools/execute":
            # Handle tool execution request
            if not session["initialized"]:
                return web.json_response({
                    "jsonrpc": "2.0",
                    "id": body.get("id"),
                    "error": {
                        "code": -32803,
                        "message": "Session not initialized"
                    }
                }, status=400)

            # Get the tool name and parameters
            params = body.get("params", {})
            tool_name = params.get("name")
            tool_params = params.get("parameters", {})

            logger.info(f"Executing tool: {tool_name} with params: {tool_params}")

            # Execute the tool
            try:
                result = await self.execute_tool(tool_name, tool_params)
                response = {
                    "jsonrpc": "2.0",
                    "id": body.get("id"),
                    "result": result
                }
                logger.info(f"Tool execution result: {result}")
                return web.json_response(response)
            except Exception as e:
                logger.error(f"Error executing tool {tool_name}: {e}")
                return web.json_response({
                    "jsonrpc": "2.0",
                    "id": body.get("id"),
                    "error": {
                        "code": -32000,
                        "message": f"Error executing tool: {str(e)}"
                    }
                }, status=500)
        else:
            # Unknown method
            method = body.get('method', 'unknown')
            logger.warning(f"Unknown method requested: {method}")
            return web.json_response({
                "jsonrpc": "2.0",
                "id": body.get("id"),
                "error": {
                    "code": -32601,
                    "message": f"Method not found: {method}"
                }
            })

    def get_tools(self):
        """Get the list of available tools."""
        return [
            {
                "name": "get_file_content",
                "description": "Get the content of a file from a repository",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "owner": {
                            "type": "string",
                            "description": "The owner of the repository"
                        },
                        "repo": {
                            "type": "string",
                            "description": "The name of the repository"
                        },
                        "ref": {
                            "type": "string",
                            "description": "The branch or commit reference",
                            "default": "main"
                        },
                        "filePath": {
                            "type": "string",
                            "description": "The path to the file"
                        }
                    },
                    "required": ["owner", "repo", "filePath"]
                },
                "returns": {
                    "type": "object",
                    "description": "File content and metadata"
                }
            },
            {
                "name": "list_branches",
                "description": "List branches in a repository",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "owner": {
                            "type": "string",
                            "description": "The owner of the repository"
                        },
                        "repo": {
                            "type": "string",
                            "description": "The name of the repository"
                        }
                    },
                    "required": ["owner", "repo"]
                },
                "returns": {
                    "type": "object",
                    "description": "List of branches in the repository"
                }
            },
            {
                "name": "list_files",
                "description": "List files in a repository directory",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "owner": {
                            "type": "string",
                            "description": "The owner of the repository"
                        },
                        "repo": {
                            "type": "string",
                            "description": "The name of the repository"
                        },
                        "path": {
                            "type": "string",
                            "description": "The directory path to list files from",
                            "default": ""
                        },
                        "ref": {
                            "type": "string",
                            "description": "The branch or commit reference",
                            "default": "main"
                        }
                    },
                    "required": ["owner", "repo"]
                },
                "returns": {
                    "type": "array",
                    "description": "List of files in the specified directory"
                }
            }
        ]

    async def execute_tool(self, tool_name, params):
        """Execute a tool with the given parameters."""
        if not tool_name:
            return {"error": "Missing tool name"}

        if not params:
            return {"error": "Missing tool parameters"}

        if tool_name == "get_file_content":
            owner = params.get("owner")
            repo = params.get("repo")
            file_path = params.get("filePath")
            ref = params.get("ref", "main")

            if not owner or not repo or not file_path:
                return {"error": "Missing required parameters: owner, repo, and filePath are required"}

            return await self.get_file_content(owner, repo, file_path, ref)

        elif tool_name == "list_branches":
            owner = params.get("owner")
            repo = params.get("repo")

            if not owner or not repo:
                return {"error": "Missing required parameters: owner and repo are required"}

            return await self.list_branches(owner, repo)

        elif tool_name == "list_files":
            owner = params.get("owner")
            repo = params.get("repo")
            path = params.get("path", "")
            ref = params.get("ref", "main")

            if not owner or not repo:
                return {"error": "Missing required parameters: owner and repo are required"}

            return await self.list_files(owner, repo, path, ref)

        else:
            return {"error": f"Unknown tool: {tool_name}"}

    async def get_file_content(self, owner, repo, file_path, ref):
        """Get the content of a file from a repository."""
        if not owner or not repo or not file_path:
            return {"error": "Missing required parameters"}

        # Set up headers for Gitea API
        headers = {"Accept": "application/json"}
        if GITEA_ACCESS_TOKEN:
            headers["Authorization"] = f"token {GITEA_ACCESS_TOKEN}"

        # Set up authentication
        auth = None
        if GITEA_USERNAME and GITEA_PASSWORD:
            auth = aiohttp.BasicAuth(GITEA_USERNAME, GITEA_PASSWORD)

        try:
            async with ClientSession() as session:
                url = f"{GITEA_URL}/api/v1/repos/{owner}/{repo}/contents/{file_path}?ref={ref}"
                async with session.get(url, headers=headers, auth=auth) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data
                    else:
                        return {"error": f"Failed to get file content: {response.status}"}
        except Exception as e:
            return {"error": f"Error getting file content: {str(e)}"}

    async def list_branches(self, owner, repo):
        """List branches in a repository."""
        if not owner or not repo:
            return {"error": "Missing required parameters"}

        # Set up headers for Gitea API
        headers = {"Accept": "application/json"}
        if GITEA_ACCESS_TOKEN:
            headers["Authorization"] = f"token {GITEA_ACCESS_TOKEN}"

        # Set up authentication
        auth = None
        if GITEA_USERNAME and GITEA_PASSWORD:
            auth = aiohttp.BasicAuth(GITEA_USERNAME, GITEA_PASSWORD)

        try:
            async with ClientSession() as session:
                url = f"{GITEA_URL}/api/v1/repos/{owner}/{repo}/branches"
                async with session.get(url, headers=headers, auth=auth) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {"branches": data}
                    else:
                        return {"error": f"Failed to list branches: {response.status}"}
        except Exception as e:
            return {"error": f"Error listing branches: {str(e)}"}

    async def list_files(self, owner, repo, path="", ref="main"):
        """List files in a repository directory."""
        if not owner or not repo:
            return {"error": "Missing required parameters"}

        # Set up headers for Gitea API
        headers = {"Accept": "application/json"}
        if GITEA_ACCESS_TOKEN:
            headers["Authorization"] = f"token {GITEA_ACCESS_TOKEN}"

        # Set up authentication
        auth = None
        if GITEA_USERNAME and GITEA_PASSWORD:
            auth = aiohttp.BasicAuth(GITEA_USERNAME, GITEA_PASSWORD)

        try:
            async with ClientSession() as session:
                url = f"{GITEA_URL}/api/v1/repos/{owner}/{repo}/contents/{path}?ref={ref}"
                async with session.get(url, headers=headers, auth=auth) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {"files": data}
                    else:
                        return {"error": f"Failed to list files: {response.status}"}
        except Exception as e:
            return {"error": f"Error listing files: {str(e)}"}

    async def handle_streamable_http(self, request):
        """Handle streamable HTTP requests."""
        logger.info("Received streamable HTTP request")

        # Generate a unique session ID
        session_id = f"session_{len(active_sessions) + 1}"
        logger.debug(f"Creating new streamable HTTP session with ID: {session_id}")

        # Create a new response
        response = web.StreamResponse()
        response.headers["Content-Type"] = "application/json"

        try:
            logger.debug(f"Preparing streamable HTTP response for session {session_id}")
            await response.prepare(request)
            logger.debug(f"Streamable HTTP response prepared for session {session_id}")

            # Store the session
            tools = self.get_tools()
            logger.debug(f"Created {len(tools)} tools for session {session_id}")

            active_sessions[session_id] = {
                "response": response,
                "initialized": False,
                "tools": tools,
                "created_at": asyncio.get_event_loop().time()
            }
            logger.info(f"Session {session_id} created and stored. Total active sessions: {len(active_sessions)}")

            # Read the request body
            body = await request.read()
            if body:
                logger.debug(f"Received request body: {body.decode()}")

                try:
                    # Parse the request as JSON
                    data = json.loads(body.decode())
                    logger.debug(f"Parsed JSON request: {data}")

                    # Handle the request
                    if data.get("method") == "initialize":
                        # Handle initialize request
                        logger.info(f"Initializing session {session_id}")
                        active_sessions[session_id]["initialized"] = True

                        # Send response according to MCP protocol
                        response_data = {
                            "jsonrpc": "2.0",
                            "id": data.get("id"),
                            "result": {
                                "capabilities": {
                                    "sampling": {},
                                    "roots": {
                                        "listChanged": True
                                    }
                                },
                                "serverInfo": {
                                    "name": "Gitea MCP Server",
                                    "version": "0.1.0",
                                    "protocolVersion": "2023-12-01"
                                }
                            }
                        }

                        logger.debug(f"Sending initialize response: {json.dumps(response_data)}")
                        await response.write(json.dumps(response_data).encode() + b"\n")
                    else:
                        # Handle other requests
                        logger.warning(f"Unhandled method: {data.get('method')}")
                        response_data = {
                            "jsonrpc": "2.0",
                            "id": data.get("id"),
                            "error": {
                                "code": -32601,
                                "message": f"Method not found: {data.get('method')}"
                            }
                        }
                        await response.write(json.dumps(response_data).encode() + b"\n")
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse JSON request body: {body.decode()}")
                    response_data = {
                        "jsonrpc": "2.0",
                        "id": None,
                        "error": {
                            "code": -32700,
                            "message": "Parse error"
                        }
                    }
                    await response.write(json.dumps(response_data).encode() + b"\n")

            # Keep the connection open
            try:
                while True:
                    await asyncio.sleep(1)
            except asyncio.CancelledError:
                # Client disconnected
                logger.info(f"Client disconnected for session {session_id}")
                if session_id in active_sessions:
                    del active_sessions[session_id]
                    logger.info(f"Session {session_id} removed. Remaining active sessions: {len(active_sessions)}")
                raise

        except Exception as e:
            logger.error(f"Error in streamable HTTP handler for session {session_id}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            if session_id in active_sessions:
                del active_sessions[session_id]
                logger.info(f"Session {session_id} removed due to error. Remaining active sessions: {len(active_sessions)}")
            raise

    def run(self):
        """Run the server."""
        logger.info(f"Starting Gitea MCP server on port {SERVER_PORT}...")
        web.run_app(self.app, port=SERVER_PORT)

if __name__ == "__main__":
    server = GiteaMCPServer()
    server.run()
