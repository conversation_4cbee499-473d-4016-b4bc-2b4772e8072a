#!/usr/bin/env python3
"""
Simplified MCP Client for connecting to the Gitea MCP Server.
This client provides a simple interface for interacting with the Gitea MCP server
without requiring the full LangChain MCP adapters setup.
"""

import os
import json
import requests
import logging
from typing import Dict, List, Any, Optional

# Configure logging
logger = logging.getLogger("MCPClient")

class MCPClient:
    """
    A simplified MCP client for connecting to the Gitea MCP server.
    This provides basic functionality for repository operations.
    """
    
    def __init__(self, server_url: str, api_key: Optional[str] = None):
        """
        Initialize the MCP client.
        
        Args:
            server_url: The URL of the Gitea MCP server
            api_key: Optional API key for authentication
        """
        self.server_url = server_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        
        # Set up headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        if self.api_key:
            self.session.headers['Authorization'] = f'Bearer {self.api_key}'
            
        logger.debug(f"Initialized MCP client for server: {self.server_url}")
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Make a request to the MCP server.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Optional request data
            
        Returns:
            Response data as dictionary
        """
        url = f"{self.server_url}/{endpoint.lstrip('/')}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=data, timeout=30)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, timeout=30)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {e}")
            return {"status": "error", "message": str(e)}
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            return {"status": "error", "message": "Invalid JSON response"}
    
    def get_repository_info(self, owner: str, repo: str, ref: str = "main") -> Dict[str, Any]:
        """
        Get repository information.
        
        Args:
            owner: Repository owner
            repo: Repository name
            ref: Branch or commit reference
            
        Returns:
            Repository information
        """
        # For the simplified client, we'll use the Gitea API directly
        # since the MCP server might not have a specific repository info endpoint
        gitea_url = os.environ.get("GITEA_URL", "http://localhost:3000")
        gitea_token = os.environ.get("GITEA_ACCESS_TOKEN")
        
        headers = {"Accept": "application/json"}
        if gitea_token:
            headers["Authorization"] = f"token {gitea_token}"
            
        try:
            url = f"{gitea_url}/api/v1/repos/{owner}/{repo}"
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                repo_data = response.json()
                return {
                    "status": "success",
                    "data": repo_data,
                    "default_branch": repo_data.get("default_branch", "main")
                }
            else:
                return {
                    "status": "error", 
                    "message": f"Failed to get repository info: {response.status_code}"
                }
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    def list_files(self, owner: str, repo: str, path: str = "", ref: str = "main") -> List[Dict[str, Any]]:
        """
        List files in a repository directory.
        
        Args:
            owner: Repository owner
            repo: Repository name
            path: Directory path (empty for root)
            ref: Branch or commit reference
            
        Returns:
            List of file information
        """
        gitea_url = os.environ.get("GITEA_URL", "http://localhost:3000")
        gitea_token = os.environ.get("GITEA_ACCESS_TOKEN")
        
        headers = {"Accept": "application/json"}
        if gitea_token:
            headers["Authorization"] = f"token {gitea_token}"
            
        try:
            url = f"{gitea_url}/api/v1/repos/{owner}/{repo}/contents/{path}"
            params = {"ref": ref} if ref else {}
            
            response = requests.get(url, headers=headers, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to list files: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"Error listing files: {e}")
            return []
    
    def get_file_content(self, owner: str, repo: str, file_path: str, ref: str = "main") -> Dict[str, Any]:
        """
        Get the content of a specific file.
        
        Args:
            owner: Repository owner
            repo: Repository name
            file_path: Path to the file
            ref: Branch or commit reference
            
        Returns:
            File content and metadata
        """
        gitea_url = os.environ.get("GITEA_URL", "http://localhost:3000")
        gitea_token = os.environ.get("GITEA_ACCESS_TOKEN")
        
        headers = {"Accept": "application/json"}
        if gitea_token:
            headers["Authorization"] = f"token {gitea_token}"
            
        try:
            url = f"{gitea_url}/api/v1/repos/{owner}/{repo}/contents/{file_path}"
            params = {"ref": ref} if ref else {}
            
            response = requests.get(url, headers=headers, params=params, timeout=30)
            
            if response.status_code == 200:
                file_data = response.json()
                return {
                    "status": "success",
                    "content": file_data.get("content", ""),
                    "encoding": file_data.get("encoding", "base64"),
                    "size": file_data.get("size", 0),
                    "sha": file_data.get("sha", ""),
                    "path": file_data.get("path", file_path)
                }
            else:
                return {
                    "status": "error",
                    "message": f"Failed to get file content: {response.status_code}"
                }
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    def list_branches(self, owner: str, repo: str) -> List[Dict[str, Any]]:
        """
        List branches in a repository.
        
        Args:
            owner: Repository owner
            repo: Repository name
            
        Returns:
            List of branch information
        """
        gitea_url = os.environ.get("GITEA_URL", "http://localhost:3000")
        gitea_token = os.environ.get("GITEA_ACCESS_TOKEN")
        
        headers = {"Accept": "application/json"}
        if gitea_token:
            headers["Authorization"] = f"token {gitea_token}"
            
        try:
            url = f"{gitea_url}/api/v1/repos/{owner}/{repo}/branches"
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to list branches: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"Error listing branches: {e}")
            return []


# Example usage
if __name__ == "__main__":
    # Test the MCP client
    server_url = os.environ.get("GITMCP_SERVER_URL", "http://localhost:8080")
    api_key = os.environ.get("GITMCP_API_KEY")
    
    client = MCPClient(server_url, api_key)
    
    # Test with a sample repository
    test_owner = "Test"
    test_repo = "Oracle"
    test_branch = "main"
    
    print(f"Testing MCP client with {test_owner}/{test_repo}")
    
    # Get repository info
    repo_info = client.get_repository_info(test_owner, test_repo, test_branch)
    print(f"Repository info: {repo_info}")
    
    # List files
    files = client.list_files(test_owner, test_repo, "", test_branch)
    print(f"Found {len(files)} files")
    
    # Get content of README.md if it exists
    readme_content = client.get_file_content(test_owner, test_repo, "README.md", test_branch)
    if readme_content.get("status") == "success":
        print("Successfully retrieved README.md content")
    else:
        print(f"Failed to get README.md: {readme_content.get('message')}")
