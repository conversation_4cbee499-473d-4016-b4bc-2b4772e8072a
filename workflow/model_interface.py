import os
import json  # Used for parsing JSON responses
from typing import Dict, Any
# In a real scenario, you would import 'openai' here
# from openai import OpenAI

# Try to import LangChain components
try:
    # Try to import from langchain-openai first (preferred)
    try:
        from langchain_openai import Chat<PERSON>penA<PERSON>
        from langchain_core.messages import HumanMessage, SystemMessage
        LANGCHAIN_OPENAI_AVAILABLE = True
    except ImportError:
        # Fall back to deprecated imports
        from langchain.chat_models import Chat<PERSON><PERSON>A<PERSON>
        from langchain.schema import HumanMessage, SystemMessage
        LANGCHAIN_OPENAI_AVAILABLE = False
        print("Warning: Using deprecated langchain imports. Please install langchain-openai.")

    # Try to import VLL<PERSON>pen<PERSON><PERSON> from langchain-community
    try:
        from langchain_community.llms.vllm import VLLMOpenAI
        VLLM_AVAILABLE = True
    except ImportError:
        VLLM_AVAILABLE = False
        print("Warning: VLLMOpenAI not available. Install with: pip install langchain-community")

    LANGCHAIN_AVAILABLE = True
except ImportError:
    print("Warning: <PERSON><PERSON>hai<PERSON> not available. Some features will be disabled.")
    LANGCHAIN_AVAILABLE = False
    LANGCHAIN_OPENAI_AVAILABLE = False
    VLLM_AVAILABLE = False

class OpenAIModelInterface:
    """
    Prepares data and sends it to the OpenAI chat model (or compatible API).
    Supports LangChain MCP adapters for connecting to the OpenAI-compatible server.
    Reads connection details from environment variables.
    """
    def __init__(self):
        self.base_url = os.environ.get("OPENAI_API_BASE_URL")
        self.api_key = os.environ.get("OPENAI_API_KEY") # May be needed
        self.mcp_tools = None
        self.langchain_model = None

        if not self.base_url:
            raise ValueError("OpenAIModelInterface: OPENAI_API_BASE_URL environment variable not set.")

        # Initialize LangChain model if available
        if LANGCHAIN_AVAILABLE:
            try:
                # Try to use VLLMOpenAI first if available
                if VLLM_AVAILABLE:
                    try:
                        # Ensure we're using the correct API base URL
                        api_base_url = self.base_url
                        print(f"OpenAIModelInterface: Using API base URL: {api_base_url}")

                        self.langchain_model = VLLMOpenAI(
                            openai_api_base=api_base_url,
                            openai_api_key=self.api_key or "dummy-key",  # Some local models require a dummy key
                            model_name=os.environ.get("OPENAI_MODEL_NAME", "gpt-3.5-turbo"),
                            temperature=0.7,
                            max_retries=3,
                            streaming=False,
                            request_timeout=180.0,  # Set explicit timeout to 180 seconds
                        )
                        print("OpenAIModelInterface: Successfully initialized VLLMOpenAI model.")
                    except Exception as e:
                        print(f"OpenAIModelInterface: Error initializing VLLMOpenAI model: {e}")
                        print("OpenAIModelInterface: Falling back to ChatOpenAI.")
                        self.langchain_model = None

                # Fall back to ChatOpenAI if VLLMOpenAI is not available or failed
                if not self.langchain_model:
                    # Ensure we're using the correct API base URL
                    api_base_url = self.base_url
                    print(f"OpenAIModelInterface: Using API base URL for ChatOpenAI: {api_base_url}")

                    self.langchain_model = ChatOpenAI(
                        base_url=api_base_url,
                        api_key=self.api_key or "dummy-key",  # Some local models require a dummy key
                        model=os.environ.get("OPENAI_MODEL_NAME", "gpt-3.5-turbo"),
                        temperature=0.7,
                        request_timeout=180.0,  # Set explicit timeout to 180 seconds
                    )
                    print("OpenAIModelInterface: Successfully initialized ChatOpenAI model.")
            except Exception as e:
                print(f"OpenAIModelInterface: Error initializing LangChain model: {e}")
                self.langchain_model = None

        # Initialize the OpenAI client here in a real scenario
        # try:
        #   self.client = OpenAI(base_url=self.base_url, api_key=self.api_key) # API key might be optional for local models
        # except Exception as e:
        #    raise RuntimeError(f"Failed to initialize OpenAI client: {e}") from e

        print(f"OpenAIModelInterface: Initialized for OpenAI model at {self.base_url}.")
        print("OpenAIModelInterface: Note - System prompt/guardrails should be configured in the model server or API call.")

    def set_mcp_tools(self, tools):
        """
        Set the MCP tools to use with the LangChain model.

        Args:
            tools: A list of LangChain tools from the MCP server.
        """
        if not LANGCHAIN_AVAILABLE:
            print("OpenAIModelInterface: LangChain not available. Cannot set MCP tools.")
            return

        self.mcp_tools = tools
        print(f"OpenAIModelInterface: Set {len(tools)} MCP tools for use with LangChain model.")

    def generate_llm_template_and_send(self, combined_context: dict) -> dict | None:
        """
        Generates the template for the LLM and sends it.
        If LangChain and MCP tools are available, it will use them to connect to the OpenAI-compatible server.
        Otherwise, it will fall back to the simulated response.
        The model should output a structured plan in JSON format.

        Args:
            combined_context: The combined issue and project context.

        Returns:
            A dictionary containing the structured plan, or None if an error occurs.
        """
        print("OpenAIModelInterface: Generating LLM template.")

        # Constructing the user prompt content for the LLM
        user_prompt_content = (
            f"Issue to address: {combined_context.get('user_issue', 'No issue specified.')}\n\n"
            f"Project Repository URL: {combined_context.get('project_repository_url', 'N/A')}\n\n"
            f"Project Branch: {combined_context.get('project_branch', 'N/A')}\n\n"
        )

        # Add project structure analysis if available
        project_analysis = combined_context.get('project_structure_analysis')
        if project_analysis:
            user_prompt_content += "Project Structure Analysis:\n"
            user_prompt_content += f"- Project Type: {project_analysis.get('project_type', 'Unknown')}\n"

            if project_analysis.get('primary_languages'):
                user_prompt_content += f"- Primary Languages: {', '.join(project_analysis.get('primary_languages', []))}\n"

            if project_analysis.get('frameworks'):
                user_prompt_content += f"- Frameworks: {', '.join(project_analysis.get('frameworks', []))}\n"

            if project_analysis.get('build_tools'):
                user_prompt_content += f"- Build Tools: {', '.join(project_analysis.get('build_tools', []))}\n"

            if project_analysis.get('key_components'):
                user_prompt_content += f"- Key Components: {', '.join(project_analysis.get('key_components', []))}\n"

            user_prompt_content += "\n"

        user_prompt_content += "Project Files Context:\n"

        files_context = combined_context.get('project_files_context', {})
        if files_context:
            # Limit context size if necessary for the model's token limit
            limited_context = {}
            context_str_len = 0
            max_len = 4000 # Example limit, adjust as needed
            for filename, content in files_context.items():
                limited_context[filename] = content[:max_len - context_str_len] # Basic truncation
                context_str_len += len(limited_context[filename])
                if context_str_len >= max_len:
                    print("OpenAIModelInterface: Warning - Truncating file context due to size limits.")
                    break

            for filename, content in limited_context.items():
                user_prompt_content += f"--- File: {filename} ---\n{content}\n---\n"
        else:
            user_prompt_content += "No file context available or retrieved.\n"
            if combined_context.get("project_repository_status"):
                user_prompt_content += f"Status from Git repository: {combined_context['project_repository_status']}\n"

        user_prompt_content += "\nBased on the issue and the provided project context, please generate a detailed plan in JSON format that can be used to guide a developer or another AI to implement the required changes or fix the issue."

        print(f"\nOpenAIModelInterface: --- Generated User Content for LLM (first 500 chars) ---")
        print(user_prompt_content[:500] + "...")
        print("----------------------------------------------------")

        # Try to use LangChain with MCP tools if available
        if LANGCHAIN_AVAILABLE and self.langchain_model and self.mcp_tools:
            try:
                print("OpenAIModelInterface: Using LangChain with MCP tools to generate plan.")

                # Create system message
                system_message = "You are a helpful AI assistant that generates structured implementation plans. Your output should be a valid JSON object."

                # Bind the MCP tools to the model
                model_with_tools = self.langchain_model.bind_tools(self.mcp_tools)

                # Create messages
                messages = [
                    SystemMessage(content=system_message),
                    HumanMessage(content=user_prompt_content)
                ]

                # Invoke the model
                response = model_with_tools.invoke(messages)

                # Try to parse the response as JSON
                try:
                    # Extract content from the response
                    content = response.content

                    # If the content is a string that looks like JSON, parse it
                    if isinstance(content, str) and content.strip().startswith('{') and content.strip().endswith('}'):
                        import json
                        plan = json.loads(content)
                        print("OpenAIModelInterface: Successfully generated plan using LangChain with MCP tools.")
                        return plan
                    # If the content is already a dictionary, return it
                    elif isinstance(content, dict):
                        print("OpenAIModelInterface: Successfully generated plan using LangChain with MCP tools.")
                        return content
                    else:
                        print(f"OpenAIModelInterface: Response from LangChain model is not in JSON format: {content}")
                        print("OpenAIModelInterface: Falling back to simulated response.")
                except Exception as e:
                    print(f"OpenAIModelInterface: Error parsing response from LangChain model: {e}")
                    print("OpenAIModelInterface: Falling back to simulated response.")
            except Exception as e:
                print(f"OpenAIModelInterface: Error using LangChain with MCP tools: {e}")
                print("OpenAIModelInterface: Falling back to simulated response.")
        else:
            # Instead of raising an error, try to use the OpenAI API directly
            if not LANGCHAIN_AVAILABLE:
                print("OpenAIModelInterface: LangChain not available. Trying direct OpenAI API call.")
                return self._direct_openai_call(combined_context)
            elif not self.langchain_model:
                print("OpenAIModelInterface: LangChain model not initialized. Trying direct OpenAI API call.")
                return self._direct_openai_call(combined_context)
            elif not self.mcp_tools:
                print("OpenAIModelInterface: MCP tools not set. Trying direct OpenAI API call.")
                return self._direct_openai_call(combined_context)
            else:
                print("OpenAIModelInterface ERROR: Unknown error. Trying direct OpenAI API call.")
                return self._direct_openai_call(combined_context)

        # This code is unreachable now
        print("OpenAIModelInterface ERROR: This code should not be reached. Please report this as a bug.")
        raise RuntimeError("This code should not be reached. Please report this as a bug.")

    def _direct_openai_call(self, combined_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a direct call to the OpenAI API without using LangChain.

        Args:
            combined_context: The combined context from the ContextBuilder.

        Returns:
            A dictionary containing the generated plan.
        """
        import requests
        import json

        print("OpenAIModelInterface: Making direct call to OpenAI API.")

        # Prepare the prompt
        user_issue = combined_context.get('user_issue', '')
        project_url = combined_context.get('project_repository_url', 'Unknown Project')
        project_branch = combined_context.get('project_branch', 'main')
        project_files = combined_context.get('project_files_context', {})

        # Extract repository name from URL
        repo_parts = project_url.split('/')
        if len(repo_parts) >= 2:
            project_name = f"{repo_parts[-2]}/{repo_parts[-1]}"
        else:
            project_name = project_url.split('/')[-1] if '/' in project_url else project_url

        # Build the prompt
        prompt = f"Issue to address: {user_issue}\\n\\n"
        prompt += f"Project Repository URL: {project_url}\\n\\n"
        prompt += f"Project Branch: {project_branch}\\n\\n"

        # Add project structure analysis if available
        project_analysis = combined_context.get('project_structure_analysis')
        if project_analysis:
            prompt += "Project Structure Analysis:\\n"
            prompt += f"- Project Type: {project_analysis.get('project_type', 'Unknown')}\\n"

            if project_analysis.get('primary_languages'):
                prompt += f"- Primary Languages: {', '.join(project_analysis.get('primary_languages', []))}\\n"

            if project_analysis.get('frameworks'):
                prompt += f"- Frameworks: {', '.join(project_analysis.get('frameworks', []))}\\n"

            if project_analysis.get('build_tools'):
                prompt += f"- Build Tools: {', '.join(project_analysis.get('build_tools', []))}\\n"

            if project_analysis.get('key_components'):
                prompt += f"- Key Components: {', '.join(project_analysis.get('key_components', []))}\\n"

            prompt += "\\n"

        prompt += "Project Files Context:\\n"

        # Include actual file contents for key files that are relevant to UI changes
        ui_relevant_files = ['frontend/index.html', 'frontend/css/style.css', 'frontend/js/app.js',
                           'index.html', 'style.css', 'app.js', 'main.css', 'main.js']

        # First, list all files
        prompt += "Project Files:\\n"
        for filename in project_files.keys():
            prompt += f"- {filename}\\n"

        # Add a brief summary of the project based on README.md
        if "README.md" in project_files:
            readme_content = project_files["README.md"]
            # Extract the first paragraph or first 500 chars, whichever is shorter
            first_paragraph = readme_content.split("\\n\\n")[0] if "\\n\\n" in readme_content else readme_content[:500]
            prompt += f"\\nProject Summary (from README.md):\\n{first_paragraph[:500]}\\n"

        # Include actual content for UI-relevant files
        prompt += "\\nKey File Contents:\\n"
        for filename, content in project_files.items():
            if any(ui_file in filename for ui_file in ui_relevant_files):
                # Truncate very long files but include enough context
                if len(content) > 2000:
                    content_preview = content[:1000] + "\\n... [truncated] ...\\n" + content[-1000:]
                else:
                    content_preview = content
                prompt += f"\\n--- File: {filename} ---\\n{content_preview}\\n"

        # Add specific instructions for UI/theme changes
        if "UI" in user_issue or "theme" in user_issue.lower() or "dark" in user_issue.lower() or "black" in user_issue.lower():
            prompt += "\\n\\nSPECIAL INSTRUCTIONS FOR UI/THEME CHANGES:"
            prompt += "\\n- Pay close attention to the CSS file structure and existing color variables"
            prompt += "\\n- If CSS variables (--variable-name) are used, leverage them for theme switching"
            prompt += "\\n- Consider both light and dark theme implementations"
            prompt += "\\n- Include specific color values in your plan (e.g., #111827 for dark backgrounds)"
            prompt += "\\n- Plan for theme persistence using localStorage"
            prompt += "\\n- Ensure accessibility and proper contrast ratios"

        # Add instructions for the plan format
        prompt += "\\n\\nBased on the issue and the provided project context, please generate a detailed plan in JSON format that can be used to implement the requested changes. The plan should include:"
        prompt += "\\n1. A summary of the user's request"
        prompt += "\\n2. The overall goal of the changes"
        prompt += "\\n3. A list of detailed steps or phases, each with:"
        prompt += "\\n   - A clear name and goal"
        prompt += "\\n   - Specific files to modify (use EXACT file paths from the project)"
        prompt += "\\n   - Detailed description of changes with specific code examples where applicable"
        prompt += "\\n   - Any commands that need to be run"
        prompt += "\\n   - Validation steps to ensure the changes work"
        prompt += "\\n\\nThe response should be a valid JSON object with the following structure:"
        prompt += "\\n{\\n  \"project_name\": \"" + project_name + "\","
        prompt += "\\n  \"branch\": \"" + project_branch + "\","
        prompt += "\\n  \"user_request_summary\": \"Brief summary of the request\","
        prompt += "\\n  \"overall_goal\": \"Overall goal of the changes\","
        prompt += "\\n  \"plan_details\": [\\n    {"
        prompt += "\\n      \"phase_name\": \"Name of the phase\","
        prompt += "\\n      \"phase_goal\": \"Goal of this phase\","
        prompt += "\\n      \"changes_description\": [\"Description of change 1\", \"Description of change 2\"],"
        prompt += "\\n      \"target_files_and_directories\": [\"/path/to/file1\", \"/path/to/file2\"],"
        prompt += "\\n      \"reason_why\": \"Explanation of why these changes are needed\","
        prompt += "\\n      \"language_tooling\": [\"Language1\", \"Tool1\"],"
        prompt += "\\n      \"required_commands_pre_change\": [\"command1\", \"command2\"],"
        prompt += "\\n      \"required_commands_post_change\": [\"command1\", \"command2\"],"
        prompt += "\\n      \"validation_steps\": [\"Step 1\", \"Step 2\"]"
        prompt += "\\n    }\\n  ],"
        prompt += "\\n  \"final_validation_script\": \"#!/bin/bash\\n# Validation script content\""
        prompt += "\\n}"

        # Print a preview of the prompt
        print("\nOpenAIModelInterface: --- Generated User Content for LLM (first 500 chars) ---")
        print(prompt[:500])
        print("----------------------------------------------------")

        # Prepare the API request
        headers = {
            "Content-Type": "application/json"
        }

        # Add API key if provided
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        # Prepare the request payload
        payload = {
            "model": os.environ.get("OPENAI_MODEL_NAME", "gpt-3.5-turbo"),  # Use model from environment or default
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful AI assistant that generates detailed implementation plans based on user requests and project context. You have expertise in web development, UI/UX design, and software architecture. When analyzing projects, pay special attention to existing code patterns, file structures, and architectural decisions. For UI changes, consider accessibility, user experience, and maintainability. Your response should be a valid JSON object following the specified structure with accurate file paths and specific implementation details."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 2000
        }

        try:
            # Implement retry logic with exponential backoff
            max_retries = 3
            retry_delay = 5  # Initial delay in seconds
            response = None

            for retry_attempt in range(max_retries):
                try:
                    # Ensure we're using the correct API base URL
                    api_base_url = self.base_url
                    api_endpoint = f"{api_base_url}/chat/completions"

                    # Make the request to the OpenAI API
                    response = requests.post(
                        api_endpoint,
                        headers=headers,
                        json=payload,
                        timeout=180  # 180 second timeout
                    )

                    # If we get here, the request was successful, so break out of the retry loop
                    break

                except requests.RequestException as e:
                    if retry_attempt < max_retries - 1:
                        # Calculate backoff time with exponential increase
                        wait_time = retry_delay * (2 ** retry_attempt)
                        import time
                        time.sleep(wait_time)
                    else:
                        raise  # Re-raise the last exception to be caught by the outer try/except

            # Check if the request was successful
            if response and response.status_code == 200:
                response_data = response.json()

                # Extract the generated content
                generated_content = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")

                # Try to parse the JSON response
                try:
                    # Find JSON content (in case there's additional text)
                    import re
                    json_match = re.search(r'({.*})', generated_content, re.DOTALL)
                    if json_match:
                        generated_content = json_match.group(1)

                    # Parse the JSON
                    plan_json = json.loads(generated_content)
                    return plan_json
                except json.JSONDecodeError as e:
                    print(f"OpenAIModelInterface: Error parsing JSON response: {e}")
                    print("OpenAIModelInterface: Raw response content:")
                    print(generated_content[:500] + "..." if len(generated_content) > 500 else generated_content)

                    # Try to create a structured plan from the unstructured response
                    return {
                        "project_name": project_name,
                        "branch": project_branch,
                        "user_request_summary": user_issue,
                        "overall_goal": f"Implement the changes required by the issue: '{user_issue}'.",
                        "plan_details": [
                            {
                                "phase_name": "Implementation",
                                "phase_goal": "Implement the requested changes",
                                "changes_description": ["See raw LLM output for details"],
                                "target_files_and_directories": [],
                                "reason_why": "Based on the LLM's analysis of the codebase",
                                "language_tooling": [],
                                "required_commands_pre_change": [],
                                "required_commands_post_change": [],
                                "validation_steps": []
                            }
                        ],
                        "raw_llm_output": generated_content
                    }
            elif response:
                print(f"OpenAIModelInterface: Error from OpenAI API - Status code: {response.status_code}")
                print(f"OpenAIModelInterface: Response: {response.text}")

                # Return a basic error plan
                return {
                    "project_name": project_name,
                    "branch": project_branch,
                    "user_request_summary": user_issue,
                    "overall_goal": f"Implement the changes required by the issue: '{user_issue}'.",
                    "plan_details": [
                        {
                            "phase_name": "Error",
                            "phase_goal": "Fix API connection issues",
                            "changes_description": [f"Error connecting to OpenAI API: Status code {response.status_code}"],
                            "target_files_and_directories": [],
                            "reason_why": "There was an error connecting to the OpenAI API",
                            "language_tooling": [],
                            "required_commands_pre_change": [],
                            "required_commands_post_change": [],
                            "validation_steps": ["Check OpenAI API connection", "Verify API key and base URL"]
                        }
                    ]
                }

        except requests.RequestException as e:
            print(f"OpenAIModelInterface: Error connecting to OpenAI API: {e}")

            # Raise the exception to be caught by the outer try/except in main.py
            raise
        except Exception as e:
            print(f"OpenAIModelInterface: Unexpected error: {e}")

            # Return a basic error plan
            return {
                "project_name": project_name,
                "branch": project_branch,
                "user_request_summary": user_issue,
                "overall_goal": f"Implement the changes required by the issue: '{user_issue}'.",
                "plan_details": [
                    {
                        "phase_name": "Error",
                        "phase_goal": "Fix unexpected error",
                        "changes_description": [f"Unexpected error: {str(e)}"],
                        "target_files_and_directories": [],
                        "reason_why": "There was an unexpected error",
                        "language_tooling": [],
                        "required_commands_pre_change": [],
                        "required_commands_post_change": [],
                        "validation_steps": ["Check error logs", "Debug the issue"]
                    }
                ]
            }

# Example of how to test this module standalone
if __name__ == "__main__":
    print("--- Testing OpenAIModelInterface Standalone ---")
    print("Ensure OPENAI_API_BASE_URL is set via 'source config.sh'")

    test_combined_context = {
        "user_issue": "Implement dark mode toggle.",
        "project_repository_url": "http://example.com/test/ui-project",
        "project_files_context": {
            "style.css": "body { background-color: white; color: black; }",
            "script.js": "// UI interaction logic"
        }
    }

    try:
        openai_iface = OpenAIModelInterface()
        generated_prompt = openai_iface.generate_llm_template_and_send(test_combined_context)

        print("\nResult (Generated Prompt - Simulated):")
        if generated_prompt:
            print("---------------------------------")
            print(generated_prompt)
            print("---------------------------------")
        else:
            print("Failed to generate prompt.")

    except ValueError as ve:
        print(f"Test Error: {ve}")
    except Exception as e:
        print(f"Test Error: An unexpected error occurred: {e}")

    print("--- End Test ---")
