import os
import sys
import json
import requests
import asyncio
import base64
import time
import logging
from typing import Dict, Optional, Any, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GiteaMCPConnector")

# Try to import our simplified MCP client
try:
    from mcp_client import MCPClient
    MCP_CLIENT_AVAILABLE = True
except ImportError:
    print("Warning: Simplified MCP client not available. Some features will be disabled.")
    print("Please ensure you have installed the required packages or created the mcp directory.")
    MCP_CLIENT_AVAILABLE = False

# Import LangChain MCP adapters
try:
    from langchain_mcp_adapters.client import MultiServerMCPClient
    from langchain_mcp_adapters.tools import load_mcp_tools
    from mcp import ClientSession
    from mcp.client.streamable_http import streamablehttp_client
    LANGCHAIN_AVAILABLE = True
except ImportError:
    logger.warning("LangChain MCP adapters not available. Some features will be disabled.")
    logger.warning("Please ensure you have installed the required packages: pip install langchain-mcp-adapters mcp")
    LANGCHAIN_AVAILABLE = False

# Import the file cache
try:
    from utils.file_cache import FileCache
    FILE_CACHE_AVAILABLE = True
except ImportError:
    logger.warning("File cache not available. File caching will be disabled.")
    logger.warning("Please ensure you have installed the required packages or created the utils directory.")
    FILE_CACHE_AVAILABLE = False

class GiteaMCPConnector:
    """
    Manages interaction with the Gitea-MCP server to fetch repository context.
    The Gitea-MCP server acts as an intermediary between Gitea and the LLM.
    Uses LangChain MCP adapters to connect to the Gitea-MCP server.
    Reads connection details from environment variables.
    """
    def __init__(self):
        self.giteamcp_server_url = os.environ.get("GITMCP_SERVER_URL")
        self.giteamcp_api_key = os.environ.get("GITMCP_API_KEY")
        self.gitea_url = os.environ.get("GITEA_URL")
        self.gitea_username = os.environ.get("GITEA_USERNAME")
        self.gitea_password = os.environ.get("GITEA_PASSWORD")
        self.gitea_access_token = os.environ.get("GITEA_ACCESS_TOKEN")
        self.openai_api_base_url = os.environ.get("OPENAI_API_BASE_URL")
        self.openai_api_key = os.environ.get("OPENAI_API_KEY")
        self.langchain_mcp_client = None
        self.mcp_client = None
        self.mcp_tools = None  # Cache for tools
        self.file_cache = None  # File cache for repository content

        # Initialize file cache if available
        if FILE_CACHE_AVAILABLE:
            try:
                cache_dir = os.environ.get("FILE_CACHE_DIR")
                max_age_seconds = int(os.environ.get("FILE_CACHE_MAX_AGE", "3600"))  # Default to 1 hour
                self.file_cache = FileCache(cache_dir=cache_dir, max_age_seconds=max_age_seconds)
                logger.info(f"File cache initialized with max age {max_age_seconds} seconds")

                # Log cache statistics
                stats = self.file_cache.get_stats()
                logger.info(f"File cache contains {stats['entries']} entries, total size: {stats['total_size_mb']:.2f} MB")
            except Exception as e:
                logger.error(f"Error initializing file cache: {e}")
                self.file_cache = None

        # Validate essential environment variables
        if not self.giteamcp_server_url:
            raise ValueError("GiteaMCPConnector: GITMCP_SERVER_URL environment variable not set.")
        if not self.gitea_url:
            raise ValueError("GiteaMCPConnector: GITEA_URL environment variable not set.")
        if not self.openai_api_base_url:
            raise ValueError("GiteaMCPConnector: OPENAI_API_BASE_URL environment variable not set.")

        # Check authentication methods
        if not self.gitea_access_token and not (self.gitea_username and self.gitea_password):
            logger.warning("Neither GITEA_ACCESS_TOKEN nor GITEA_USERNAME/PASSWORD are set. Assuming anonymous access if possible.")
        elif self.gitea_access_token:
            logger.debug("Using Gitea access token for authentication.")
        else:
            logger.debug("Using Gitea username/password for authentication.")

        logger.debug(f"Initialized for Gitea-MCP server at {self.giteamcp_server_url}")
        logger.debug(f"Using Gitea instance at {self.gitea_url}")
        logger.debug(f"Using OpenAI-compatible server at {self.openai_api_base_url}")

        # Initialize simplified MCP client if available
        if MCP_CLIENT_AVAILABLE:
            try:
                self.mcp_client = MCPClient(self.giteamcp_server_url, self.giteamcp_api_key)
                logger.debug("GiteaMCPConnector: Successfully initialized simplified MCP client.")
            except Exception as e:
                logger.debug(f"GiteaMCPConnector: Error initializing simplified MCP client: {e}")
                self.mcp_client = None

        # Initialize LangChain MCP client if available
        if LANGCHAIN_AVAILABLE:
            self._init_langchain_mcp_client()

    def _init_langchain_mcp_client(self):
        """
        Initialize the LangChain MCP client.
        This sets up the connection to the Gitea-MCP server using LangChain MCP adapters.
        """
        if not LANGCHAIN_AVAILABLE:
            print("GiteaMCPConnector: LangChain MCP adapters not available. Cannot initialize LangChain MCP client.")
            return

        try:
            # Configure the MCP client with the Gitea-MCP server
            self.langchain_mcp_client = MultiServerMCPClient(
                {
                    "giteamcp": {
                        "transport": "streamable_http",
                        "url": f"{self.giteamcp_server_url}",  # Base URL - adapter will handle SSE endpoint discovery
                        # Add API key if provided
                        **({"api_key": self.giteamcp_api_key} if self.giteamcp_api_key else {})
                    }
                }
            )
            logger.debug("GiteaMCPConnector: Successfully initialized LangChain MCP client.")
        except Exception as e:
            logger.debug(f"GiteaMCPConnector: Error initializing LangChain MCP client: {e}")
            self.langchain_mcp_client = None

    async def _get_mcp_tools_async(self):
        """
        Get the MCP tools from the Gitea-MCP server asynchronously.
        This is an async method that needs to be run in an event loop.

        Returns:
            A list of LangChain tools that can be used with the LLM.
        """
        if not LANGCHAIN_AVAILABLE or not self.langchain_mcp_client:
            print("GiteaMCPConnector: LangChain MCP adapters not available or client not initialized.")
            return None

        try:
            # Get the tools from the MCP client
            tools = await self.langchain_mcp_client.get_tools()
            print(f"GiteaMCPConnector: Successfully loaded {len(tools)} MCP tools.")

            # Print tool names for debugging
            print("Available tools:")
            for tool in tools:
                print(f"- {tool.name}: {tool.description}")

            return tools
        except ConnectionRefusedError as e:
            print(f"GiteaMCPConnector: Connection refused to Gitea-MCP server at {self.giteamcp_server_url}. Is the server running?")
            return None
        except Exception as e:
            print(f"GiteaMCPConnector: Error getting MCP tools: {e}")
            # Print more detailed error information
            import traceback
            print("GiteaMCPConnector: Detailed error traceback:")
            traceback.print_exc()
            return None

    def get_mcp_tools(self):
        """
        Synchronous wrapper for _get_mcp_tools_async.
        Caches tools to avoid repeated fetching.

        Returns:
            A list of LangChain tools that can be used with the LLM.
        """
        # Return cached tools if available
        if self.mcp_tools:
            return self.mcp_tools

        if not LANGCHAIN_AVAILABLE:
            print("GiteaMCPConnector: LangChain MCP adapters not available.")
            return None

        try:
            # Run the async method in an event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            tools = loop.run_until_complete(self._get_mcp_tools_async())
            loop.close()

            # Cache the tools for future use
            self.mcp_tools = tools
            return tools
        except Exception as e:
            print(f"GiteaMCPConnector: Error getting MCP tools: {e}")
            # Print more detailed error information
            import traceback
            print("GiteaMCPConnector: Detailed error traceback:")
            traceback.print_exc()
            return None

    def get_project_repository_context(self, repository_identifier: str, branch_name: str) -> Optional[Dict[str, Any]]:
        """
        Fetches repository context from the Gitea-MCP server for the given repository and branch.
        If our simplified MCP client is available, it will use it to connect to the Gitea API.
        If LangChain MCP adapters are available, it will use them to connect to the Gitea-MCP server.
        Otherwise, it will fall back to the direct HTTP request method.

        Args:
            repository_identifier: The repository identifier (e.g., "owner/repo_name").
            branch_name: The branch to analyze.

        Returns:
            A dictionary containing the project URL, branch, and file contents,
            or None if an error occurs.
        """
        print(f"GiteaMCPConnector: Requesting context for repository '{repository_identifier}', branch '{branch_name}'.")

        # Split repository identifier into owner and repo
        if "/" not in repository_identifier:
            print(f"GiteaMCPConnector: Invalid repository identifier: {repository_identifier}. Expected format: owner/repo")
            return None

        owner, repo = repository_identifier.split("/", 1)

        # Try to use our simplified MCP client if available
        if MCP_CLIENT_AVAILABLE and self.mcp_client:
            try:
                print("GiteaMCPConnector: Using simplified MCP client to fetch repository context.")

                # Get repository info
                repo_info = self.mcp_client.get_repository_info(owner, repo, branch_name)
                if repo_info.get("status") == "error":
                    print(f"GiteaMCPConnector: Error getting repository info: {repo_info.get('message')}")
                    print("GiteaMCPConnector: Trying LangChain MCP adapters...")
                else:
                    # Get a list of files
                    files_list = self.mcp_client.list_files(owner, repo, "", branch_name)
                    if isinstance(files_list, list):
                        # Process the file list and fetch content for each file
                        files_content = {}
                        for file_info in files_list:
                            if file_info.get("type") == "file":
                                file_path = file_info.get("path")
                                try:
                                    # Check if the file is in the cache
                                    cached_content = None
                                    if self.file_cache:
                                        cached_content = self.file_cache.get(owner, repo, file_path, branch_name)
                                        if cached_content:
                                            logger.debug(f"Cache hit for {file_path}")
                                            files_content[file_path] = cached_content
                                            continue

                                    # If not in cache, get the file content
                                    file_content = self.mcp_client.get_file_content(owner, repo, file_path, branch_name)
                                    if file_content.get("status") != "error" and "content" in file_content:
                                        if file_content.get("encoding") == "base64":
                                            # Check if the file is likely binary based on extension
                                            file_extension = file_path.split('.')[-1].lower() if '.' in file_path else ''
                                            binary_extensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'ico', 'pdf', 'zip', 'gz',
                                                                'tar', 'exe', 'dll', 'so', 'bin', 'dat', 'o', 'pyc', 'class']

                                            is_likely_binary = file_extension in binary_extensions

                                            if is_likely_binary:
                                                # For binary files, just provide a placeholder
                                                logger.debug(f"Detected binary file: {file_path}")
                                                content = f"[Binary file: {file_path} - {file_content.get('size', 0)} bytes]"
                                                files_content[file_path] = content

                                                # Cache the content
                                                if self.file_cache:
                                                    self.file_cache.put(owner, repo, file_path, branch_name, content, file_content.get("sha"))
                                            else:
                                                try:
                                                    # Try to decode as UTF-8
                                                    decoded_content = base64.b64decode(file_content["content"]).decode("utf-8")
                                                    files_content[file_path] = decoded_content

                                                    # Cache the content
                                                    if self.file_cache:
                                                        self.file_cache.put(owner, repo, file_path, branch_name, decoded_content, file_content.get("sha"))
                                                except UnicodeDecodeError:
                                                    # If decoding fails, it's probably a binary file
                                                    logger.debug(f"Failed to decode as UTF-8, treating as binary: {file_path}")
                                                    content = f"[Binary file: {file_path} - {file_content.get('size', 0)} bytes]"
                                                    files_content[file_path] = content

                                                    # Cache the content
                                                    if self.file_cache:
                                                        self.file_cache.put(owner, repo, file_path, branch_name, content, file_content.get("sha"))
                                                except Exception as e:
                                                    logger.error(f"Error decoding base64 content for {file_path}: {e}")
                                        else:
                                            content = file_content["content"]
                                            files_content[file_path] = content

                                            # Cache the content
                                            if self.file_cache:
                                                self.file_cache.put(owner, repo, file_path, branch_name, content, file_content.get("sha"))
                                except Exception as e:
                                    logger.error(f"Error getting file content for {file_path}: {e}")
                                    continue

                        if files_content:
                            # Return the repository context
                            return {
                                "project_url": f"{self.gitea_url}/{repository_identifier}",
                                "branch": branch_name,
                                "files": files_content,
                                "message": "Successfully fetched repository context via simplified MCP client."
                            }
                        else:
                            print("GiteaMCPConnector: No files content retrieved. Trying LangChain MCP adapters...")
                    else:
                        print("GiteaMCPConnector: Error getting files list. Trying LangChain MCP adapters...")
            except Exception as e:
                print(f"GiteaMCPConnector: Error using simplified MCP client: {e}")
                print("GiteaMCPConnector: Trying LangChain MCP adapters...")

        # Try to use LangChain MCP adapters if available
        if LANGCHAIN_AVAILABLE and self.langchain_mcp_client:
            try:
                # Get the tools from the MCP client
                tools = self.get_mcp_tools()
                if tools:
                    print("GiteaMCPConnector: Using LangChain MCP adapters to fetch repository context.")

                    # Find the appropriate tools for repository operations
                    # Based on the Gitea-MCP server implementation
                    get_file_content_tool = None
                    list_branches_tool = None

                    # Look for the specific tools we need
                    for tool in tools:
                        if tool.name == "get_file_content":
                            get_file_content_tool = tool
                        elif tool.name == "list_branches":
                            list_branches_tool = tool

                    # Print found tools for debugging
                    print(f"Found get_file_content_tool: {get_file_content_tool.name if get_file_content_tool else None}")
                    print(f"Found list_branches_tool: {list_branches_tool.name if list_branches_tool else None}")

                    # Get a list of files to fetch
                    # Since Gitea-MCP doesn't have a direct "list files" tool, we'll use a predefined list of common files
                    # Expanded to include more relevant files for different project types
                    common_files = [
                        # Project documentation (highest priority)
                        "README.md",
                        "CONTRIBUTING.md",
                        "CHANGELOG.md",
                        "docs/README.md",
                        "CODE_OF_CONDUCT.md",
                        "ARCHITECTURE.md",
                        "docs/ARCHITECTURE.md",
                        "docs/architecture.md",
                        "docs/design.md",
                        "docs/DESIGN.md",
                        "docs/overview.md",
                        "docs/OVERVIEW.md",

                        # Configuration files (high priority)
                        ".gitignore",
                        ".env.example",
                        "config.json",
                        "config.yaml",
                        "config.yml",
                        "config.js",
                        "config.py",
                        "config.sh",
                        "config/config.go",
                        "config/settings.go",
                        "config/settings.py",
                        "config/settings.js",

                        # Package management (high priority - helps identify language/framework)
                        "package.json",
                        "package-lock.json",
                        "yarn.lock",
                        "requirements.txt",
                        "pyproject.toml",
                        "setup.py",
                        "go.mod",
                        "go.sum",
                        "Cargo.toml",
                        "Gemfile",
                        "composer.json",
                        "build.gradle",
                        "pom.xml",
                        "Pipfile",

                        # Docker/deployment (medium priority)
                        "Dockerfile",
                        "docker-compose.yml",
                        ".dockerignore",
                        "kubernetes/",
                        "k8s/",
                        "helm/",
                        "deploy/",
                        "deployment/",

                        # Main application files (high priority)
                        "main.py",
                        "app.py",
                        "server.py",
                        "main.go",
                        "main.js",
                        "index.js",
                        "server.js",
                        "app.js",
                        "cmd/main.go",
                        "cmd/server/main.go",
                        "cmd/app/main.go",

                        # Web frameworks (medium priority)
                        "next.config.js",
                        "nuxt.config.js",
                        "vite.config.js",
                        "webpack.config.js",
                        "tsconfig.json",
                        "angular.json",
                        "vue.config.js",
                        "svelte.config.js",

                        # API/Backend (high priority)
                        "api/index.js",
                        "api/routes.js",
                        "api/app.py",
                        "backend/main.py",
                        "backend/app.py",
                        "backend/server.js",
                        "src/main.py",
                        "src/main.js",
                        "src/main.go",
                        "src/app.py",
                        "internal/app/",
                        "internal/server/",
                        "internal/api/",
                        "pkg/api/",
                        "pkg/server/",

                        # Authentication related files (medium priority)
                        "auth.js",
                        "auth.py",
                        "auth.go",
                        "authentication.js",
                        "authentication.py",
                        "middleware/auth.js",
                        "middleware/auth.py",
                        "middleware/authentication.js",
                        "middleware/authentication.py",
                        "utils/auth.js",
                        "utils/auth.py",
                        "services/auth.js",
                        "services/auth.py",
                        "backend/auth.py",
                        "backend/auth.js",
                        "backend/app/auth.py",
                        "backend/app/auth.js",

                        # Frontend/Web files (medium priority)
                        "index.html",
                        "index.htm",
                        "main.html",
                        "app.html",
                        "src/index.html",
                        "public/index.html",
                        "web/index.html",
                        "static/index.html",
                        "assets/index.html",
                        "frontend/index.html",  # Added for Forge/maestro project
                        "frontend/css/style.css",  # Added for Forge/maestro project
                        "frontend/js/main.js",  # Added for Forge/maestro project
                        "frontend/js/app.js",  # Added for Forge/maestro project
                        "css/style.css",
                        "css/main.css",
                        "css/app.css",
                        "styles/style.css",
                        "styles/main.css",
                        "js/main.js",
                        "js/app.js",
                        "js/index.js",
                        "scripts/main.js",
                        "scripts/app.js",

                        # Workflow-specific files (highest priority for this project)
                        "workflow/main.py",
                        "workflow/config.sh",
                        "workflow/1_workflow_trigger.py",
                        "workflow/2_giteamcp_connector.py",
                        "workflow/3_context_builder.py",
                        "workflow/4_openai_model_interface.py",
                        "workflow/mcp/langgraph_agent.py",
                        "workflow/mcp/langgraph_mcp_connector.py",
                        "workflow/mcp/simplified_mcp_client.py",
                        "workflow/start_gitea_mcp_server.py"
                    ]

                    # Process the file list and fetch content for each file
                    files_content = {}
                    if get_file_content_tool:
                        for file_path in common_files:
                            try:
                                # Check if the file is in the cache
                                cached_content = None
                                if self.file_cache:
                                    cached_content = self.file_cache.get(owner, repo, file_path, branch_name)
                                    if cached_content:
                                        logger.debug(f"Cache hit for {file_path}")
                                        files_content[file_path] = cached_content
                                        continue

                                # Use the get_file_content tool with the correct parameters
                                file_content = get_file_content_tool.invoke({
                                    "owner": owner,
                                    "repo": repo,
                                    "ref": branch_name,
                                    "filePath": file_path
                                })

                                # Handle the response
                                if isinstance(file_content, dict):
                                    if 'content' in file_content:
                                        if file_content.get('encoding') == 'base64':
                                            # Check if the file is likely binary based on extension
                                            file_extension = file_path.split('.')[-1].lower() if '.' in file_path else ''
                                            binary_extensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'ico', 'pdf', 'zip', 'gz',
                                                                'tar', 'exe', 'dll', 'so', 'bin', 'dat', 'o', 'pyc', 'class']

                                            is_likely_binary = file_extension in binary_extensions

                                            if is_likely_binary:
                                                # For binary files, just provide a placeholder
                                                logger.debug(f"Detected binary file: {file_path}")
                                                content = f"[Binary file: {file_path} - {file_content.get('size', 0)} bytes]"
                                                files_content[file_path] = content

                                                # Cache the content
                                                if self.file_cache:
                                                    self.file_cache.put(owner, repo, file_path, branch_name, content, file_content.get("sha"))
                                            else:
                                                try:
                                                    decoded_content = base64.b64decode(file_content['content']).decode('utf-8')
                                                    files_content[file_path] = decoded_content

                                                    # Cache the content
                                                    if self.file_cache:
                                                        self.file_cache.put(owner, repo, file_path, branch_name, decoded_content, file_content.get("sha"))
                                                except UnicodeDecodeError:
                                                    # If decoding fails, it's probably a binary file
                                                    logger.debug(f"Failed to decode as UTF-8, treating as binary: {file_path}")
                                                    content = f"[Binary file: {file_path} - {file_content.get('size', 0)} bytes]"
                                                    files_content[file_path] = content

                                                    # Cache the content
                                                    if self.file_cache:
                                                        self.file_cache.put(owner, repo, file_path, branch_name, content, file_content.get("sha"))
                                                except Exception as e:
                                                    logger.error(f"Error decoding base64 content for {file_path}: {e}")
                                        else:
                                            content = file_content['content']
                                            files_content[file_path] = content

                                            # Cache the content
                                            if self.file_cache:
                                                self.file_cache.put(owner, repo, file_path, branch_name, content, file_content.get("sha"))
                                    else:
                                        # If content is not in the expected format, store the whole response
                                        content = str(file_content)
                                        files_content[file_path] = content

                                        # Cache the content
                                        if self.file_cache:
                                            self.file_cache.put(owner, repo, file_path, branch_name, content)
                                else:
                                    # If the response is not a dict, store it as is
                                    content = str(file_content)
                                    files_content[file_path] = content

                                    # Cache the content
                                    if self.file_cache:
                                        self.file_cache.put(owner, repo, file_path, branch_name, content)
                            except Exception as e:
                                # Skip files that don't exist or can't be accessed
                                logger.error(f"File {file_path} not found or error: {e}")
                                continue

                    if files_content:
                        # Return the repository context
                        return {
                            "project_url": f"{self.gitea_url}/{repository_identifier}",
                            "branch": branch_name,
                            "files": files_content,
                            "message": "Successfully fetched repository context via Gitea-MCP using LangChain MCP adapters."
                        }
                    else:
                        print("GiteaMCPConnector: No files content retrieved. Falling back to direct Gitea API.")
                else:
                    print("GiteaMCPConnector: Failed to get MCP tools. Falling back to direct Gitea API.")
            except Exception as e:
                print(f"GiteaMCPConnector: Error using LangChain MCP adapters: {e}")
                print("GiteaMCPConnector: Falling back to direct Gitea API.")

        # Fall back to direct Gitea API
        return self._direct_http_fallback(repository_identifier, branch_name)

    def _direct_http_fallback(self, repository_identifier: str, branch_name: str) -> Optional[Dict[str, Any]]:
        """
        Fallback method that attempts to fetch repository context via direct Gitea API requests.
        This is used when LangChain MCP adapters are not available or fail.

        Args:
            repository_identifier: The repository identifier (e.g., "owner/repo_name").
            branch_name: The branch to analyze.

        Returns:
            A dictionary containing the project URL, branch, and file contents,
            or None if an error occurs.
        """
        print("GiteaMCPConnector: Using direct Gitea API as fallback.")

        # Split repository identifier into owner and repo
        if "/" not in repository_identifier:
            print(f"GiteaMCPConnector: Invalid repository identifier: {repository_identifier}. Expected format: owner/repo")
            return None

        owner, repo = repository_identifier.split("/", 1)

        # Set up authentication for direct Gitea API
        auth = None
        if self.gitea_username and self.gitea_password:
            auth = (self.gitea_username, self.gitea_password)

        # Update headers for Gitea API
        gitea_headers = {
            "Accept": "application/json"
        }

        if self.gitea_access_token:
            gitea_headers["Authorization"] = f"token {self.gitea_access_token}"

        # Common files to check - using the same expanded list as in the MCP method
        common_files = [
            # Project documentation (highest priority)
            "README.md",
            "CONTRIBUTING.md",
            "CHANGELOG.md",
            "docs/README.md",
            "CODE_OF_CONDUCT.md",
            "ARCHITECTURE.md",
            "docs/ARCHITECTURE.md",
            "docs/architecture.md",
            "docs/design.md",
            "docs/DESIGN.md",
            "docs/overview.md",
            "docs/OVERVIEW.md",

            # Configuration files (high priority)
            ".gitignore",
            ".env.example",
            "config.json",
            "config.yaml",
            "config.yml",
            "config.js",
            "config.py",
            "config.sh",
            "config/config.go",
            "config/settings.go",
            "config/settings.py",
            "config/settings.js",

            # Package management (high priority - helps identify language/framework)
            "package.json",
            "package-lock.json",
            "yarn.lock",
            "requirements.txt",
            "pyproject.toml",
            "setup.py",
            "go.mod",
            "go.sum",
            "Cargo.toml",
            "Gemfile",
            "composer.json",
            "build.gradle",
            "pom.xml",
            "Pipfile",

            # Docker/deployment (medium priority)
            "Dockerfile",
            "docker-compose.yml",
            ".dockerignore",
            "kubernetes/",
            "k8s/",
            "helm/",
            "deploy/",
            "deployment/",

            # Main application files (high priority)
            "main.py",
            "app.py",
            "server.py",
            "main.go",
            "main.js",
            "index.js",
            "server.js",
            "app.js",
            "cmd/main.go",
            "cmd/server/main.go",
            "cmd/app/main.go",

            # Web frameworks (medium priority)
            "next.config.js",
            "nuxt.config.js",
            "vite.config.js",
            "webpack.config.js",
            "tsconfig.json",
            "angular.json",
            "vue.config.js",
            "svelte.config.js",

            # API/Backend (high priority)
            "api/index.js",
            "api/routes.js",
            "api/app.py",
            "backend/main.py",
            "backend/app.py",
            "backend/server.js",
            "src/main.py",
            "src/main.js",
            "src/main.go",
            "src/app.py",
            "internal/app/",
            "internal/server/",
            "internal/api/",
            "pkg/api/",
            "pkg/server/",

            # Authentication related files (medium priority)
            "auth.js",
            "auth.py",
            "auth.go",
            "authentication.js",
            "authentication.py",
            "middleware/auth.js",
            "middleware/auth.py",
            "middleware/authentication.js",
            "middleware/authentication.py",
            "utils/auth.js",
            "utils/auth.py",
            "services/auth.js",
            "services/auth.py",
            "backend/auth.py",
            "backend/auth.js",
            "backend/app/auth.py",
            "backend/app/auth.js",

            # Frontend/Web files (medium priority)
            "index.html",
            "index.htm",
            "main.html",
            "app.html",
            "src/index.html",
            "public/index.html",
            "web/index.html",
            "static/index.html",
            "assets/index.html",
            "frontend/index.html",  # Added for Forge/maestro project
            "frontend/css/style.css",  # Added for Forge/maestro project
            "frontend/js/main.js",  # Added for Forge/maestro project
            "frontend/js/app.js",  # Added for Forge/maestro project
            "css/style.css",
            "css/main.css",
            "css/app.css",
            "styles/style.css",
            "styles/main.css",
            "js/main.js",
            "js/app.js",
            "js/index.js",
            "scripts/main.js",
            "scripts/app.js",

            # Workflow-specific files (highest priority for this project)
            "workflow/main.py",
            "workflow/config.sh",
            "workflow/1_workflow_trigger.py",
            "workflow/2_giteamcp_connector.py",
            "workflow/3_context_builder.py",
            "workflow/4_openai_model_interface.py",
            "workflow/mcp/langgraph_agent.py",
            "workflow/mcp/langgraph_mcp_connector.py",
            "workflow/mcp/simplified_mcp_client.py",
            "workflow/start_gitea_mcp_server.py"
        ]

        # Get repository contents
        files_content = {}

        # First, try to get the root directory contents
        try:
            # This is the standard Gitea API endpoint for getting repository contents
            api_url = f"{self.gitea_url}/api/v1/repos/{owner}/{repo}/contents"

            # Add branch parameter if provided
            params = {"ref": branch_name} if branch_name else {}

            # Make the request to the Gitea API
            response = requests.get(
                api_url,
                headers=gitea_headers,
                auth=auth,
                params=params,
                timeout=30
            )

            if response.status_code == 200:
                # Get the list of files in the root directory
                root_files = response.json()

                # Process the files
                for file_info in root_files:
                    if file_info.get("type") == "file" and file_info.get("name") in common_files:
                        file_path = file_info.get("path")

                        # Check if the file is in the cache
                        cached_content = None
                        if self.file_cache:
                            cached_content = self.file_cache.get(owner, repo, file_path, branch_name)
                            if cached_content:
                                logger.debug(f"Cache hit for {file_path}")
                                files_content[file_path] = cached_content
                                continue

                        # Get the file content
                        try:
                            file_url = f"{self.gitea_url}/api/v1/repos/{owner}/{repo}/contents/{file_path}"
                            file_response = requests.get(
                                file_url,
                                headers=gitea_headers,
                                auth=auth,
                                params=params,
                                timeout=30
                            )

                            if file_response.status_code == 200:
                                file_data = file_response.json()
                                if file_data.get("encoding") == "base64" and file_data.get("content"):
                                    # Check if the file is likely binary based on extension
                                    file_extension = file_path.split('.')[-1].lower() if '.' in file_path else ''
                                    binary_extensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'ico', 'pdf', 'zip', 'gz',
                                                        'tar', 'exe', 'dll', 'so', 'bin', 'dat', 'o', 'pyc', 'class']

                                    is_likely_binary = file_extension in binary_extensions

                                    if is_likely_binary:
                                        # For binary files, just provide a placeholder
                                        logger.debug(f"Detected binary file: {file_path}")
                                        content = f"[Binary file: {file_path} - {file_data.get('size', 0)} bytes]"
                                        files_content[file_path] = content

                                        # Cache the content
                                        if self.file_cache:
                                            self.file_cache.put(owner, repo, file_path, branch_name, content, file_data.get("sha"))
                                    else:
                                        try:
                                            # Try to decode as UTF-8
                                            decoded_content = base64.b64decode(file_data["content"]).decode('utf-8')
                                            files_content[file_path] = decoded_content

                                            # Cache the content
                                            if self.file_cache:
                                                self.file_cache.put(owner, repo, file_path, branch_name, decoded_content, file_data.get("sha"))
                                        except UnicodeDecodeError:
                                            # If decoding fails, it's probably a binary file
                                            logger.debug(f"Failed to decode as UTF-8, treating as binary: {file_path}")
                                            content = f"[Binary file: {file_path} - {file_data.get('size', 0)} bytes]"
                                            files_content[file_path] = content

                                            # Cache the content
                                            if self.file_cache:
                                                self.file_cache.put(owner, repo, file_path, branch_name, content, file_data.get("sha"))
                                        except Exception as e:
                                            logger.error(f"Error decoding base64 content for {file_path}: {e}")
                        except Exception as e:
                            logger.error(f"Error getting file content for {file_path}: {e}")
            else:
                print(f"GiteaMCPConnector: Error from Gitea API - Status code: {response.status_code}")
                print(f"GiteaMCPConnector: Response: {response.text}")
        except Exception as e:
            print(f"GiteaMCPConnector: Error with Gitea API: {e}")

        # If we couldn't get any files from the root directory, try the common files directly
        if not files_content:
            for file_path in common_files:
                # Check if the file is in the cache
                cached_content = None
                if self.file_cache:
                    cached_content = self.file_cache.get(owner, repo, file_path, branch_name)
                    if cached_content:
                        logger.debug(f"Cache hit for {file_path}")
                        files_content[file_path] = cached_content
                        continue

                try:
                    file_url = f"{self.gitea_url}/api/v1/repos/{owner}/{repo}/contents/{file_path}"
                    file_response = requests.get(
                        file_url,
                        headers=gitea_headers,
                        auth=auth,
                        params={"ref": branch_name},
                        timeout=30
                    )

                    if file_response.status_code == 200:
                        file_data = file_response.json()
                        if file_data.get("encoding") == "base64" and file_data.get("content"):
                            # Check if the file is likely binary based on extension
                            file_extension = file_path.split('.')[-1].lower() if '.' in file_path else ''
                            binary_extensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'ico', 'pdf', 'zip', 'gz',
                                                'tar', 'exe', 'dll', 'so', 'bin', 'dat', 'o', 'pyc', 'class']

                            is_likely_binary = file_extension in binary_extensions

                            if is_likely_binary:
                                # For binary files, just provide a placeholder
                                logger.debug(f"Detected binary file: {file_path}")
                                content = f"[Binary file: {file_path} - {file_data.get('size', 0)} bytes]"
                                files_content[file_path] = content

                                # Cache the content
                                if self.file_cache:
                                    self.file_cache.put(owner, repo, file_path, branch_name, content, file_data.get("sha"))
                            else:
                                try:
                                    decoded_content = base64.b64decode(file_data["content"]).decode('utf-8')
                                    files_content[file_path] = decoded_content

                                    # Cache the content
                                    if self.file_cache:
                                        self.file_cache.put(owner, repo, file_path, branch_name, decoded_content, file_data.get("sha"))
                                except UnicodeDecodeError:
                                    # If decoding fails, it's probably a binary file
                                    logger.debug(f"Failed to decode as UTF-8, treating as binary: {file_path}")
                                    content = f"[Binary file: {file_path} - {file_data.get('size', 0)} bytes]"
                                    files_content[file_path] = content

                                    # Cache the content
                                    if self.file_cache:
                                        self.file_cache.put(owner, repo, file_path, branch_name, content, file_data.get("sha"))
                                except Exception as e:
                                    logger.error(f"Error decoding base64 content for {file_path}: {e}")
                except Exception as e:
                    # Skip files that don't exist
                    logger.debug(f"File {file_path} not found or error: {e}")
                    continue

        if files_content:
            return {
                "project_url": f"{self.gitea_url}/{repository_identifier}",
                "branch": branch_name,
                "files": files_content,
                "message": "Successfully fetched repository context via direct Gitea API."
            }
        else:
            print(f"GiteaMCPConnector: Could not retrieve any files from repository {repository_identifier}")
            return None


# Example of how to test this module standalone
if __name__ == "__main__":
    print("--- Testing GiteaMCPConnector Standalone ---")
    print("Ensure GITMCP_SERVER_URL, GITEA_URL, etc. are set via 'source config.sh'")

    try:
        gitmcp_connector = GiteaMCPConnector()
        test_repo = "Test/Oracle"
        test_branch = "main"

        # Try to get actual context from Gitea-MCP server
        try:
            print("\nAttempting to connect to Gitea-MCP server...")
            context = gitmcp_connector.get_project_repository_context(test_repo, test_branch)
        except requests.RequestException as e:
            print(f"\nCould not connect to Gitea-MCP server: {e}")
            print("Please check that the Gitea-MCP server is running and accessible.")
            sys.exit(1)
        except Exception as e:
            print(f"\nUnexpected error: {e}")
            sys.exit(1)

        if context:
            print("\nResult (Repository Context):")
            print(f"Project URL: {context.get('project_url')}")
            print(f"Branch: {context.get('branch')}")
            print(f"Number of files: {len(context.get('files', {}))}")
            print("\nFile list:")
            for filename in context.get('files', {}).keys():
                print(f"- {filename}")

            # Print a sample of the first file content
            if context.get('files'):
                first_file = next(iter(context.get('files')))
                content = context.get('files')[first_file]
                print(f"\nSample content from {first_file} (first 200 chars):")
                print(content[:200] + ("..." if len(content) > 200 else ""))
        else:
            print("\nFailed to retrieve repository context.")
            print("Please check the Gitea-MCP server logs for more information.")
            sys.exit(1)

    except ValueError as ve:
        print(f"Test Error: {ve}")
        sys.exit(1)
    except Exception as e:
        print(f"Test Error: An unexpected error occurred: {e}")
        sys.exit(1)

    print("--- End Test ---")
