#!/usr/bin/env python3
"""
Workflow - AI-Powered Development Workflow System
Main entry point for the workflow system.

This orchestrates the entire workflow:
1. User input and project selection
2. Repository context fetching via Gitea-MCP
3. Context building and optimization
4. AI model interface for plan generation
5. Output generation and validation
"""

import sys
import json
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import workflow components
from trigger import WorkflowTrigger
from gitea_connector import GiteaMCPConnector
from context_builder import ContextBuilder
from model_interface import OpenAIModelInterface
from utils.banner import display_banner

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main workflow orchestrator."""
    try:
        # Print welcome banner
        display_banner()

        # Step 1: Initialize workflow trigger (user input)
        print("🚀 Initializing Workflow System...")
        trigger = WorkflowTrigger()

        # Get user input through interactive interface
        user_input = trigger.get_user_input()
        if not user_input:
            print("❌ No input provided. Exiting.")
            return 1

        repository_identifier = user_input.get("repository_identifier")
        branch_name = user_input.get("branch_name")
        user_query = user_input.get("user_query")

        print(f"✅ Project: {repository_identifier}")
        print(f"✅ Branch: {branch_name}")
        print(f"✅ Query: {user_query}")

        # Step 2: Initialize Gitea-MCP connector
        print("\n🔗 Connecting to Gitea via MCP...")
        gitea_connector = GiteaMCPConnector()

        # Fetch repository context
        repository_context = gitea_connector.get_project_repository_context(
            repository_identifier, branch_name
        )

        if not repository_context:
            print("❌ Failed to fetch repository context. Exiting.")
            return 1

        print(f"✅ Retrieved context for {len(repository_context.get('files', {}))} files")

        # Step 3: Build combined context
        print("\n🧠 Building context...")
        context_builder = ContextBuilder()

        combined_context = context_builder.build_combined_context(
            user_input, repository_context
        )

        if not combined_context:
            print("❌ Failed to build combined context. Exiting.")
            return 1

        print("✅ Context built successfully")

        # Step 4: Generate implementation plan using AI
        print("\n🤖 Generating implementation plan...")
        model_interface = OpenAIModelInterface()

        # Set MCP tools if available
        mcp_tools = gitea_connector.get_mcp_tools()
        if mcp_tools:
            model_interface.set_mcp_tools(mcp_tools)
            print(f"✅ Loaded {len(mcp_tools)} MCP tools")

        # Generate the plan
        implementation_plan = model_interface.generate_llm_template_and_send(combined_context)

        if not implementation_plan:
            print("❌ Failed to generate implementation plan. Exiting.")
            return 1

        print("✅ Implementation plan generated")

        # Step 5: Save output (relative to project root)
        output_dir = project_root / f"run_output_{repository_identifier.replace('/', '_')}_{branch_name}"
        output_dir.mkdir(exist_ok=True)

        plan_file = output_dir / "plan.json"
        with open(plan_file, "w", encoding="utf-8") as f:
            json.dump(implementation_plan, f, indent=2, ensure_ascii=False)

        print(f"\n✅ Workflow completed successfully!")
        print(f"📁 Output saved to: {plan_file}")

        # Display summary
        if isinstance(implementation_plan, dict):
            phases = implementation_plan.get("phases", [])
            if phases:
                print(f"📋 Generated plan with {len(phases)} phases")
                for i, phase in enumerate(phases, 1):
                    print(f"   {i}. {phase.get('name', f'Phase {i}')}")

        return 0

    except KeyboardInterrupt:
        print("\n\n⚠️ Workflow interrupted by user.")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error in main workflow: {e}")
        print(f"\n❌ Error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
