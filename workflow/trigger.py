import json
import os
from typing import Dict, Optional, Tuple

# Import the banner utility
try:
    from utils.banner import print_banner
    BANNER_AVAILABLE = True
except ImportError:
    print("Warning: Banner utility not available. Some visual features will be disabled.")
    BANNER_AVAILABLE = False

class WorkflowTrigger:
    """
    Handles the initial activation of the workflow by the user.
    Provides both interactive and programmatic interfaces.
    """
    def __init__(self):
        print("WorkflowTrigger: Initialized.")

        # Common projects for quick selection
        self.common_projects = [
            "Forge/maestro",
            "Test/Oracle",
        ]

        # Common branches for quick selection
        self.common_branches = [
            "main",
            "master",
            "develop",
            "dev"
        ]

    def get_user_input(self) -> Optional[Dict[str, str]]:
        """
        Interactive interface to get user input for the workflow.

        Returns:
            Dictionary with repository_identifier, branch_name, and user_query
        """
        try:
            print("\n" + "="*60)
            print("🚀 WORKFLOW SYSTEM - INTERACTIVE MODE")
            print("="*60)

            # Step 1: Project selection
            repository_identifier = self._get_project_selection()
            if not repository_identifier:
                return None

            # Step 2: Branch selection
            branch_name = self._get_branch_selection()
            if not branch_name:
                return None

            # Step 3: User query
            user_query = self._get_user_query()
            if not user_query:
                return None

            # Step 4: Confirmation
            if not self._confirm_workflow(repository_identifier, branch_name, user_query):
                return None

            return {
                "repository_identifier": repository_identifier,
                "branch_name": branch_name,
                "user_query": user_query
            }

        except KeyboardInterrupt:
            print("\n\n⚠️ Workflow cancelled by user.")
            return None
        except Exception as e:
            print(f"\n❌ Error getting user input: {e}")
            return None

    def submit_issue(self, user_input_string: str) -> Optional[Tuple[str, str, str]]:
        """
        Parses the user's input to separate the project name, branch name, and the issue explanation.

        Args:
            user_input_string: A string like "Forge/Maestro,main,please edit the ui..."

        Returns:
            A tuple containing (project_name, branch_name, issue_explanation) or None if parsing fails.
        """
        print(f"WorkflowTrigger: Received raw input: '{user_input_string}'")
        try:
            parts = user_input_string.split(',', 2)
            if len(parts) == 3:
                project_name = parts[0].strip()
                branch_name = parts[1].strip()
                issue_explanation = parts[2].strip()

                # Validate that all parts are non-empty
                if not project_name or not branch_name or not issue_explanation:
                    print("WorkflowTrigger: Error - Project name, branch name, or issue explanation is empty after parsing.")
                    print("WorkflowTrigger: Expected format: 'Project/Repo,Branch,Description'")
                    return None

                print(f"WorkflowTrigger: Parsed Project Name: '{project_name}'")
                print(f"WorkflowTrigger: Parsed Branch Name: '{branch_name}'")
                print(f"WorkflowTrigger: Parsed Issue Explanation: '{issue_explanation}'")
                return project_name, branch_name, issue_explanation
            else:
                print("WorkflowTrigger: Error - Input string must contain two commas separating project name, branch name, and issue.")
                print("WorkflowTrigger: Expected format: 'Project/Repo,Branch,Description'")
                return None
        except Exception as e:
            print(f"WorkflowTrigger: Error parsing issue input: {e}")
            return None

    def _get_project_selection(self) -> Optional[str]:
        """Get project selection from user."""
        print("\n🔍 Select a project:")
        for i, project in enumerate(self.common_projects, 1):
            print(f"  {i}. {project}")
        print(f"  {len(self.common_projects) + 1}. Enter custom project...")

        while True:
            try:
                choice = input(f"\nSelect option (1-{len(self.common_projects) + 1}): ").strip()

                if choice.isdigit():
                    choice_num = int(choice)
                    if 1 <= choice_num <= len(self.common_projects):
                        selected = self.common_projects[choice_num - 1]
                        print(f"✅ Selected project: {selected}")
                        return selected
                    elif choice_num == len(self.common_projects) + 1:
                        custom = input("Enter project (format: owner/repo): ").strip()
                        if custom and "/" in custom:
                            print(f"✅ Selected project: {custom}")
                            return custom
                        else:
                            print("❌ Invalid format. Please use 'owner/repo' format.")
                            continue

                print(f"❌ Invalid choice. Please enter 1-{len(self.common_projects) + 1}")

            except (ValueError, KeyboardInterrupt):
                return None

    def _get_branch_selection(self) -> Optional[str]:
        """Get branch selection from user."""
        print("\n🔍 Select a branch:")
        for i, branch in enumerate(self.common_branches, 1):
            print(f"  {i}. {branch}")
        print(f"  {len(self.common_branches) + 1}. Enter custom branch...")

        while True:
            try:
                choice = input(f"\nSelect option (1-{len(self.common_branches) + 1}): ").strip()

                if choice.isdigit():
                    choice_num = int(choice)
                    if 1 <= choice_num <= len(self.common_branches):
                        selected = self.common_branches[choice_num - 1]
                        print(f"✅ Selected branch: {selected}")
                        return selected
                    elif choice_num == len(self.common_branches) + 1:
                        custom = input("Enter branch name: ").strip()
                        if custom:
                            print(f"✅ Selected branch: {custom}")
                            return custom
                        else:
                            print("❌ Branch name cannot be empty.")
                            continue

                print(f"❌ Invalid choice. Please enter 1-{len(self.common_branches) + 1}")

            except (ValueError, KeyboardInterrupt):
                return None

    def _get_user_query(self) -> Optional[str]:
        """Get user query/request."""
        print("\n🤖 Enter your request:")
        print("   (Describe what you want to implement or change)")

        try:
            query = input("Your request: ").strip()
            if query:
                return query
            else:
                print("❌ Request cannot be empty.")
                return None
        except KeyboardInterrupt:
            return None

    def _confirm_workflow(self, repository_identifier: str, branch_name: str, user_query: str) -> bool:
        """Confirm workflow execution with user."""
        print("\n" + "="*60)
        print("📋 WORKFLOW SUMMARY")
        print("="*60)
        print(f"🏗️  Project: {repository_identifier}")
        print(f"🌿 Branch: {branch_name}")
        print(f"🤖 Request: {user_query}")
        print("="*60)

        try:
            confirm = input("\n🚀 Proceed with workflow? (y/N): ").strip().lower()
            return confirm in ['y', 'yes']
        except KeyboardInterrupt:
            return False

# Example of how to test this module standalone
if __name__ == "__main__":
    print("--- Testing WorkflowTrigger Standalone ---")
    trigger = WorkflowTrigger()
    test_input_good = "Forge/Maestro,main,Test issue description"
    test_input_bad_format = "NoCommaHere"
    test_input_missing_branch = "Forge/Maestro,Test issue description"

    result_good = trigger.submit_issue(test_input_good)
    print(f"Result (Good Input): {result_good}")

    result_bad_format = trigger.submit_issue(test_input_bad_format)
    print(f"Result (Bad Format): {result_bad_format}")

    result_missing_branch = trigger.submit_issue(test_input_missing_branch)
    print(f"Result (Missing Branch): {result_missing_branch}")
    print("--- End Test ---")
