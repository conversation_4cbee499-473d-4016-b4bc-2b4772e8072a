import json
import os
import logging
import sys
from typing import Dict, Any, Optional, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("ContextBuilder")

# Import the context optimizer
try:
    # Add the parent directory to the path so we can import the utils module
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.context_optimizer import ContextOptimizer
    CONTEXT_OPTIMIZER_AVAILABLE = True
except ImportError:
    logger.warning("Context optimizer not available. Token optimization will be limited.")
    CONTEXT_OPTIMIZER_AVAILABLE = False

class ContextBuilder:
    """
    Combines the issue details and Gitea project context.
    """
    def __init__(self):
        print("ContextBuilder: Initialized.")
        logger.info("Initializing ContextBuilder")

        # Check if context optimizer is available
        self.context_optimizer_available = CONTEXT_OPTIMIZER_AVAILABLE
        if self.context_optimizer_available:
            logger.info("Context optimizer is available")
        else:
            logger.warning("Context optimizer is not available. Token optimization will be limited.")

        # Maximum tokens for the entire context
        self.max_total_tokens = int(os.environ.get("MAX_TOTAL_TOKENS", "6000"))

    def analyze_project_structure(self, project_files: dict) -> dict:
        """
        Analyzes the project structure to identify languages, frameworks, and key components.

        Args:
            project_files: Dictionary of project files and their contents.

        Returns:
            A dictionary containing analysis of the project structure.
        """
        print("ContextBuilder: Analyzing project structure...")

        # Initialize analysis results
        analysis = {
            "primary_languages": [],
            "frameworks": [],
            "build_tools": [],
            "key_components": [],
            "project_type": "unknown"
        }

        # Count file extensions to determine primary languages
        extensions = {}
        for filename in project_files.keys():
            if '.' in filename:
                ext = filename.split('.')[-1].lower()
                extensions[ext] = extensions.get(ext, 0) + 1

        # Map extensions to languages
        extension_to_language = {
            'py': 'Python',
            'js': 'JavaScript',
            'ts': 'TypeScript',
            'go': 'Go',
            'java': 'Java',
            'rb': 'Ruby',
            'php': 'PHP',
            'cs': 'C#',
            'cpp': 'C++',
            'c': 'C',
            'rs': 'Rust',
            'swift': 'Swift',
            'kt': 'Kotlin',
            'scala': 'Scala',
            'html': 'HTML',
            'css': 'CSS',
            'scss': 'SCSS',
            'sass': 'Sass',
            'md': 'Markdown',
            'json': 'JSON',
            'yaml': 'YAML',
            'yml': 'YAML',
            'xml': 'XML',
            'sh': 'Shell',
            'bash': 'Bash',
            'sql': 'SQL'
        }

        # Determine primary languages
        if extensions:
            sorted_extensions = sorted(extensions.items(), key=lambda x: x[1], reverse=True)
            for ext, count in sorted_extensions[:3]:  # Top 3 extensions
                if ext in extension_to_language:
                    analysis["primary_languages"].append(extension_to_language[ext])

        # Check for specific files to identify frameworks and build tools
        framework_indicators = {
            'package.json': ('Node.js', 'npm'),
            'yarn.lock': ('Node.js', 'Yarn'),
            'requirements.txt': ('Python', 'pip'),
            'pyproject.toml': ('Python', 'Poetry/Pip'),
            'setup.py': ('Python', 'setuptools'),
            'go.mod': ('Go', 'Go Modules'),
            'Cargo.toml': ('Rust', 'Cargo'),
            'pom.xml': ('Java', 'Maven'),
            'build.gradle': ('Java/Kotlin', 'Gradle'),
            'Gemfile': ('Ruby', 'Bundler'),
            'composer.json': ('PHP', 'Composer'),
            'Dockerfile': ('Docker', None),
            'docker-compose.yml': ('Docker Compose', None),
            'kubernetes/': ('Kubernetes', None),
            'k8s/': ('Kubernetes', None)
        }

        # Framework-specific files
        web_framework_indicators = {
            'next.config.js': 'Next.js',
            'nuxt.config.js': 'Nuxt.js',
            'angular.json': 'Angular',
            'vue.config.js': 'Vue.js',
            'svelte.config.js': 'Svelte',
            'webpack.config.js': 'Webpack',
            'tsconfig.json': 'TypeScript',
            'django': 'Django',
            'flask': 'Flask',
            'fastapi': 'FastAPI',
            'express': 'Express.js',
            'spring': 'Spring',
            'rails': 'Ruby on Rails',
            'laravel': 'Laravel',
            'gin': 'Gin',
            'echo': 'Echo',
            'fiber': 'Fiber'
        }

        # Check for framework indicators in files
        for filename, content in project_files.items():
            # Check for framework indicators in filenames
            for indicator, (lang, build_tool) in framework_indicators.items():
                if indicator in filename:
                    if lang and lang not in analysis["primary_languages"]:
                        analysis["primary_languages"].append(lang)
                    if build_tool and build_tool not in analysis["build_tools"]:
                        analysis["build_tools"].append(build_tool)

            # Check for web framework indicators
            for indicator, framework in web_framework_indicators.items():
                if indicator in filename or (content and indicator.lower() in content.lower()):
                    if framework not in analysis["frameworks"]:
                        analysis["frameworks"].append(framework)

            # Check for workflow-specific files
            if 'workflow' in filename and filename not in analysis["key_components"]:
                analysis["key_components"].append(filename)

        # Determine project type
        if 'workflow/main.py' in project_files or 'workflow/config.sh' in project_files:
            analysis["project_type"] = "Workflow System"
        elif 'Dockerfile' in project_files or 'docker-compose.yml' in project_files:
            analysis["project_type"] = "Containerized Application"
        elif any(f for f in project_files.keys() if 'api/' in f or 'backend/' in f):
            analysis["project_type"] = "Backend/API Service"
        elif any(f for f in project_files.keys() if f.endswith('.html') or f.endswith('.css') or f.endswith('.js')):
            analysis["project_type"] = "Web Application"

        print(f"ContextBuilder: Project structure analysis completed: {json.dumps(analysis, indent=2)}")
        return analysis

    def combine_issue_and_project_context(self, issue_explanation: str, project_context: dict | None) -> dict:
        """
        Combines the user's issue with the fetched project context.

        Args:
            issue_explanation: The explanation of the issue from the user.
            project_context: The context fetched from Git repository (can be None if fetch failed).

        Returns:
            A dictionary containing the combined information.
        """
        print("ContextBuilder: Combining issue and project context.")
        logger.info("Combining issue and project context")

        if not project_context or not project_context.get("files"):
            logger.warning("Project context is missing, empty, or missing files. Proceeding with available data.")
            print("ContextBuilder: Warning - Project context is missing, empty, or missing files. Proceeding with available data.")
            combined_data = {
                "user_issue": issue_explanation,
                "project_repository_status": "No files or context retrieved/provided from Git repository.",
                "project_files_context": {},
                "project_structure_analysis": {
                    "primary_languages": [],
                    "frameworks": [],
                    "build_tools": [],
                    "key_components": [],
                    "project_type": "unknown"
                }
            }
        else:
            # Analyze project structure
            project_files = project_context.get("files", {})
            logger.info(f"Analyzing project structure with {len(project_files)} files")
            project_structure_analysis = self.analyze_project_structure(project_files)

            # Create the combined data
            combined_data = {
                "user_issue": issue_explanation,
                "project_repository_url": project_context.get("project_url", "N/A"),
                "project_branch": project_context.get("branch", "N/A"),  # Include branch information
                "project_files_context": project_files,
                "project_structure_analysis": project_structure_analysis
            }

            # Optimize the context if the optimizer is available
            if self.context_optimizer_available:
                logger.info(f"Optimizing context with max tokens: {self.max_total_tokens}")
                print(f"ContextBuilder: Optimizing context with max tokens: {self.max_total_tokens}")

                # Estimate current token usage
                total_chars = sum(len(content) for content in project_files.values())
                estimated_tokens = total_chars // 4  # Rough estimate: 4 chars per token
                logger.info(f"Estimated current token usage: {estimated_tokens} tokens")

                if estimated_tokens > self.max_total_tokens:
                    # Optimize the context
                    try:
                        optimized_data = ContextOptimizer.optimize_context(
                            combined_data,
                            issue_explanation,
                            self.max_total_tokens
                        )

                        # Log the optimization results
                        original_files = len(combined_data["project_files_context"])
                        optimized_files = len(optimized_data["project_files_context"])
                        logger.info(f"Context optimized: {original_files} files -> {optimized_files} files")
                        print(f"ContextBuilder: Context optimized: {original_files} files -> {optimized_files} files")

                        # Update the combined data
                        combined_data = optimized_data
                    except Exception as e:
                        logger.error(f"Error optimizing context: {str(e)}")
                        logger.error("Using unoptimized context")
                        print(f"ContextBuilder: Error optimizing context: {str(e)}")
                else:
                    logger.info("Context is already within token limits. No optimization needed.")
            else:
                # Simple truncation if optimizer is not available
                logger.info("Context optimizer not available. Using simple truncation.")

                # Truncate large files to avoid exceeding token limits
                truncated_files = {}
                for file_path, content in project_files.items():
                    if len(content) > 10000:  # Roughly 2500 tokens
                        truncated_content = content[:5000] + "\n\n[...content truncated...]\n\n" + content[-5000:]
                        truncated_files[file_path] = truncated_content
                        logger.info(f"Truncated large file: {file_path}")
                        print(f"ContextBuilder: Truncated large file: {file_path}")
                    else:
                        truncated_files[file_path] = content

                # Update the combined data
                combined_data["project_files_context"] = truncated_files

        logger.info("Combined context created")
        print(f"ContextBuilder: Combined context created: {json.dumps(combined_data, indent=2)}")
        return combined_data

# Example of how to test this module standalone
if __name__ == "__main__":
    print("--- Testing ContextBuilder Standalone ---")
    builder = ContextBuilder()
    test_issue = "Fix the login button color."
    test_context_good = {
        "project_url": "http://example.com/test/repo",
        "branch": "main",
        "files": {"login.css": "button { color: blue; }", "index.html": "<html>...</html>"},
        "message": "Simulated success"
    }
    test_context_bad = None
    test_context_empty = {"project_url": "http://example.com/test/empty", "branch": "develop", "files": {}}

    combined_good = builder.combine_issue_and_project_context(test_issue, test_context_good)
    print("\nCombined (Good Context):")
    print(json.dumps(combined_good, indent=2))

    combined_bad = builder.combine_issue_and_project_context(test_issue, test_context_bad)
    print("\nCombined (Bad Context - None):")
    print(json.dumps(combined_bad, indent=2))

    combined_empty = builder.combine_issue_and_project_context(test_issue, test_context_empty)
    print("\nCombined (Empty Files Context):")
    print(json.dumps(combined_empty, indent=2))

    print("--- End Test ---")
