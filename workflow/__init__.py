"""
Workflow - AI-Powered Development Workflow System

Core workflow components for generating structured implementation plans
from natural language requests using AI and repository context.
"""

__version__ = "1.0.0"
__author__ = "Workflow Team"
__description__ = "AI-Powered Development Workflow System"

from .trigger import WorkflowTrigger
from .gitea_connector import GiteaMCPConnector
from .context_builder import Context<PERSON><PERSON>er
from .model_interface import OpenAIModelInterface

__all__ = [
    "WorkflowTrigger",
    "GiteaMCPConnector", 
    "ContextBuilder",
    "OpenAIModelInterface"
]
