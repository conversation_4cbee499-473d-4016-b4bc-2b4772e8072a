{"name": "Gitea MCP Workflow System", "version": "1.0.0", "description": "AI-powered development workflow system that integrates with Gitea repositories via Model Context Protocol (MCP) to provide intelligent, context-aware planning and code retrieval", "repository_root": "/home/<USER>/Documents/TEST_PROJECTS/gitea-mcp-ide", "workflow_components": {"main_orchestrator": {"file": "workflow/main.py", "description": "Main entry point and workflow orchestrator", "functions": ["main()", "orchestrate_workflow_steps()"]}, "gitea_connector": {"file": "workflow/gitea_connector.py", "class": "GiteaMCPConnector", "description": "Manages interaction with Gitea-MCP server to fetch repository context", "key_methods": ["get_project_repository_context(repository_identifier, branch_name)", "get_mcp_tools()", "_direct_http_fallback(repository_identifier, branch_name)", "_init_langchain_mcp_client()"], "capabilities": ["Repository context fetching via MCP", "Direct Gitea API fallback", "LangChain MCP adapter integration", "File content retrieval with priority lists"]}, "workflow_trigger": {"file": "workflow/trigger.py", "class": "WorkflowTrigger", "description": "Handles user input parsing and validation", "key_methods": ["get_user_input()", "submit_issue(user_input_string)", "_get_project_selection()", "_get_branch_selection()", "_get_user_query()"]}, "context_builder": {"file": "workflow/context_builder.py", "class": "ContextBuilder", "description": "Combines user input and repository context for AI processing", "key_methods": ["build_combined_context(user_input, repository_context)"]}, "model_interface": {"file": "workflow/model_interface.py", "class": "OpenAIModelInterface", "description": "AI model interface for plan generation using OpenAI-compatible APIs", "key_methods": ["generate_llm_template_and_send(combined_context)", "set_mcp_tools(mcp_tools)", "_direct_openai_call()", "_create_system_prompt()"]}}, "mcp_components": {"server": {"file": "mcp/server.py", "class": "GiteaMCPServer", "description": "MCP-compatible server providing interface to Gitea repositories", "port": 8080, "endpoints": ["/version", "/", "SSE streaming support"], "capabilities": ["RESTful API with MCP protocol compliance", "Server-Sent Events (SSE) support", "Multiple authentication methods", "Tool-based repository operations"]}, "client": {"file": "mcp/client.py", "class": "MCPClient", "description": "Simplified MCP client for repository operations", "key_methods": ["get_repository_info(owner, repo, branch)", "list_files(owner, repo, path, branch)", "get_file_content(owner, repo, file_path, branch)", "_make_request(method, endpoint, data)"]}}, "environment_variables": {"required": {"GITEA_URL": "Gitea instance URL (default: http://localhost:3000)", "GITEA_ACCESS_TOKEN": "Personal access token for Gitea authentication", "GITMCP_SERVER_URL": "MCP server endpoint (default: http://localhost:8080)", "OPENAI_API_BASE_URL": "AI model API endpoint (default: http://localhost:8001/v1)", "OPENAI_API_KEY": "API authentication key"}, "optional": {"GITMCP_API_KEY": "Optional API key for MCP server authentication", "GITEA_USERNAME": "Gitea username (alternative to access token)", "GITEA_PASSWORD": "Gitea password (alternative to access token)", "OPENAI_MODEL_NAME": "Model identifier (default: gpt-3.5-turbo)", "LOCAL_REPO_CLONE_PATH": "Local repository clone path", "FILE_CACHE_DIR": "File cache directory", "FILE_CACHE_MAX_AGE": "File cache maximum age in seconds"}}, "configuration_files": {"linux_mac": "config/config.sh", "windows": "config/config.bat", "local_config": "config/local_config.sh (not committed to version control)"}, "workflow_steps": [{"step": 1, "name": "User Input Processing", "component": "WorkflowTrigger", "description": "Parse and validate user input for repository and request details"}, {"step": 2, "name": "Repository Context Fetching", "component": "GiteaMCPConnector", "description": "Fetch repository context via MCP server or direct Gitea API"}, {"step": 3, "name": "Context Building", "component": "ContextBuilder", "description": "Combine user input and repository context for AI processing"}, {"step": 4, "name": "Plan Generation", "component": "OpenAIModelInterface", "description": "Generate implementation plan using AI model with MCP tools"}, {"step": 5, "name": "Output Generation", "component": "Main Orchestrator", "description": "Save generated plan and provide structured output"}], "priority_files": ["workflow/main.py", "workflow/config.sh", "workflow/1_workflow_trigger.py", "workflow/2_giteamcp_connector.py", "workflow/3_context_builder.py", "workflow/4_openai_model_interface.py", "workflow/mcp/langgraph_agent.py", "workflow/mcp/langgraph_mcp_connector.py", "workflow/mcp/simplified_mcp_client.py", "workflow/start_gitea_mcp_server.py"], "usage_examples": {"interactive_mode": {"command": "cd workflow && python main.py", "description": "Run workflow in interactive mode with user prompts"}, "programmatic_usage": {"example": "trigger.submit_issue('Forge/maestro,main,Add dark mode toggle')", "description": "Use workflow components programmatically"}, "server_startup": {"command": "cd mcp && python server.py", "description": "Start the Gitea MCP server independently"}}, "dependencies": {"required": ["requests", "aiohttp", "python-dotenv"], "optional": ["langchain-mcp-adapters", "mcp", "langchain-openai", "langchain-community"]}, "output_format": {"directory": "run_output_{repository}_{branch}", "files": ["plan.json", "context.json", "metadata.json"]}, "mcp_tools_available": {"description": "Tools available through the MCP server for repository operations", "tools": [{"name": "get_repository_info", "description": "Get basic repository information including metadata and structure"}, {"name": "list_files", "description": "List files and directories in a repository path"}, {"name": "get_file_content", "description": "Retrieve content of specific files from repository"}, {"name": "search_files", "description": "Search for files matching specific patterns or criteria"}]}, "api_endpoints": {"mcp_server": {"base_url": "http://localhost:8080", "endpoints": {"/version": {"method": "GET", "description": "Get server version and status information"}, "/": {"method": "POST", "description": "Main MCP protocol endpoint for tool execution", "content_type": "application/json"}, "/sse": {"method": "GET", "description": "Server-Sent Events endpoint for real-time communication"}}}, "gitea_api": {"base_url": "{GITEA_URL}/api/v1", "authentication": "Bearer token or Basic auth", "fallback_endpoints": ["/repos/{owner}/{repo}", "/repos/{owner}/{repo}/contents/{path}", "/repos/{owner}/{repo}/branches", "/repos/{owner}/{repo}/commits"]}}, "integration_patterns": {"augment_usage": {"description": "How Augment can use this workflow to automatically pull code", "steps": ["1. Parse user request for repository and feature information", "2. Use GiteaMCPConnector.get_project_repository_context() to fetch relevant code", "3. Apply priority file filtering to focus on most relevant files", "4. Use MCP tools for targeted file retrieval and content analysis", "5. Build context using ContextBuilder for comprehensive understanding"], "key_functions_for_augment": ["GiteaMCPConnector.get_project_repository_context(repo_id, branch)", "GiteaMCPConnector.get_mcp_tools()", "ContextBuilder.build_combined_context(user_input, repo_context)", "MCPClient.get_file_content(owner, repo, file_path, branch)"]}, "automatic_code_retrieval": {"trigger_patterns": ["User mentions specific repository (e.g., 'Forge/maestro')", "User requests features from repos inside Gitea", "User asks for code analysis or implementation help"], "retrieval_strategy": ["Use priority file list for initial context", "Expand to related files based on dependencies", "Apply intelligent filtering based on user request context", "Cache results for performance optimization"]}}, "error_handling": {"fallback_mechanisms": ["MCP server unavailable → Direct Gitea API", "Authentication failure → Anonymous access (if allowed)", "Network timeout → Cached content (if available)", "Tool execution failure → Alternative tool or manual retrieval"], "retry_strategies": ["Exponential backoff for network requests", "Alternative authentication methods", "Graceful degradation of functionality"]}, "performance_optimizations": {"caching": {"file_cache_dir": "$HOME/.workflow_cache", "cache_duration": "3600 seconds (configurable)", "cache_strategy": "LRU with size limits"}, "request_optimization": ["Batch file requests when possible", "Prioritize frequently accessed files", "Use streaming for large file transfers", "Implement request deduplication"]}}