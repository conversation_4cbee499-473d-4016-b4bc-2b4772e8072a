{"name": "Gitea MCP Workflow System", "version": "1.0.0", "description": "AI-powered development workflow system that integrates with Gitea repositories via Model Context Protocol (MCP) to provide intelligent, context-aware planning and code retrieval", "repository_root": "/home/<USER>/Documents/TEST_PROJECTS/gitea-mcp-ide", "workflow_components": {"main_orchestrator": {"file": "workflow/main.py", "description": "Main entry point and workflow orchestrator", "functions": ["main()", "orchestrate_workflow_steps()"]}, "gitea_connector": {"file": "workflow/gitea_connector.py", "class": "GiteaMCPConnector", "description": "Manages interaction with Gitea-MCP server to fetch repository context", "key_methods": ["get_project_repository_context(repository_identifier, branch_name)", "get_mcp_tools()", "_direct_http_fallback(repository_identifier, branch_name)", "_init_langchain_mcp_client()"], "capabilities": ["Repository context fetching via MCP", "Direct Gitea API fallback", "LangChain MCP adapter integration", "File content retrieval with priority lists"]}, "workflow_trigger": {"file": "workflow/trigger.py", "class": "WorkflowTrigger", "description": "Handles user input parsing and validation", "key_methods": ["get_user_input()", "submit_issue(user_input_string)", "_get_project_selection()", "_get_branch_selection()", "_get_user_query()"]}, "context_builder": {"file": "workflow/context_builder.py", "class": "ContextBuilder", "description": "Combines user input and repository context for AI processing", "key_methods": ["build_combined_context(user_input, repository_context)"]}, "model_interface": {"file": "workflow/model_interface.py", "class": "OpenAIModelInterface", "description": "AI model interface for plan generation - NOTE: Augment uses MCP directly, not this interface", "key_methods": ["generate_llm_template_and_send(combined_context)", "set_mcp_tools(mcp_tools)", "_direct_openai_call()", "_create_system_prompt()"], "augment_note": "Augment bypasses this interface and uses MCP tools directly via stdio"}}, "mcp_components": {"server": {"file": "mcp/server.py", "class": "GiteaMCPServer", "description": "MCP-compatible server providing interface to Gitea repositories", "transport": "stdio", "protocol": "MCP over stdio for Augment integration", "capabilities": ["Standard input/output communication", "JSON-RPC protocol compliance", "Multiple authentication methods", "Tool-based repository operations", "Direct process communication"]}, "client": {"file": "mcp/client.py", "class": "MCPClient", "description": "Simplified MCP client for repository operations", "key_methods": ["get_repository_info(owner, repo, branch)", "list_files(owner, repo, path, branch)", "get_file_content(owner, repo, file_path, branch)", "_make_request(method, endpoint, data)"]}}, "environment_variables": {"required_for_augment": {"GITEA_URL": "Gitea instance URL (default: http://localhost:3000)", "GITEA_ACCESS_TOKEN": "Personal access token for Gitea authentication"}, "optional_for_augment": {"GITEA_USERNAME": "Gitea username (alternative to access token)", "GITEA_PASSWORD": "Gitea password (alternative to access token)", "LOCAL_REPO_CLONE_PATH": "Local repository clone path", "FILE_CACHE_DIR": "File cache directory", "FILE_CACHE_MAX_AGE": "File cache maximum age in seconds"}, "not_needed_for_augment": {"GITMCP_SERVER_URL": "Not needed - Augment uses stdio transport", "GITMCP_API_KEY": "Not needed - Augment uses stdio transport", "OPENAI_API_BASE_URL": "Not needed - Augment is the AI model", "OPENAI_API_KEY": "Not needed - Augment is the AI model", "OPENAI_MODEL_NAME": "Not needed - Augment is the AI model"}}, "configuration_files": {"linux_mac": "config/config.sh", "windows": "config/config.bat", "local_config": "config/local_config.sh (not committed to version control)"}, "workflow_steps": [{"step": 1, "name": "User Input Processing", "component": "WorkflowTrigger", "description": "Parse and validate user input for repository and request details"}, {"step": 2, "name": "Repository Context Fetching", "component": "GiteaMCPConnector", "description": "Fetch repository context via MCP server or direct Gitea API"}, {"step": 3, "name": "Context Building", "component": "ContextBuilder", "description": "Combine user input and repository context for AI processing"}, {"step": 4, "name": "Plan Generation", "component": "Augment AI Model", "description": "Augment uses MCP tools directly to understand code and generate plans"}, {"step": 5, "name": "Output Generation", "component": "Main Orchestrator", "description": "Save generated plan and provide structured output"}], "priority_files": ["workflow/main.py", "workflow/config.sh", "workflow/1_workflow_trigger.py", "workflow/2_giteamcp_connector.py", "workflow/3_context_builder.py", "workflow/4_openai_model_interface.py", "workflow/mcp/langgraph_agent.py", "workflow/mcp/langgraph_mcp_connector.py", "workflow/mcp/simplified_mcp_client.py", "workflow/start_gitea_mcp_server.py"], "usage_examples": {"interactive_mode": {"command": "cd workflow && python main.py", "description": "Run workflow in interactive mode with user prompts"}, "programmatic_usage": {"example": "trigger.submit_issue('Forge/maestro,main,Add dark mode toggle')", "description": "Use workflow components programmatically"}, "server_startup": {"command": "cd mcp && python server.py", "description": "Start the Gitea MCP server independently"}}, "dependencies": {"required": ["requests", "aiohttp", "python-dotenv"], "optional": ["langchain-mcp-adapters", "mcp", "langchain-openai", "langchain-community"]}, "output_format": {"directory": "run_output_{repository}_{branch}", "files": ["plan.json", "context.json", "metadata.json"]}, "mcp_tools_available": {"description": "Tools available through the MCP server for repository operations", "tools": [{"name": "get_repository_info", "description": "Get basic repository information including metadata and structure"}, {"name": "list_files", "description": "List files and directories in a repository path"}, {"name": "get_file_content", "description": "Retrieve content of specific files from repository"}, {"name": "search_files", "description": "Search for files matching specific patterns or criteria"}]}, "api_endpoints": {"mcp_server": {"transport": "stdio", "protocol": "JSON-RPC over stdin/stdout", "communication": {"input": "stdin", "output": "stdout", "format": "JSON-RPC 2.0", "description": "Direct process communication via standard input/output streams"}, "initialization": {"method": "initialize", "description": "Initialize MCP session with capabilities exchange"}}, "gitea_api": {"base_url": "{GITEA_URL}/api/v1", "authentication": "Bearer token or Basic auth", "fallback_endpoints": ["/repos/{owner}/{repo}", "/repos/{owner}/{repo}/contents/{path}", "/repos/{owner}/{repo}/branches", "/repos/{owner}/{repo}/commits"]}}, "integration_patterns": {"augment_usage": {"description": "How Augment can use this workflow to automatically pull code via stdio MCP", "transport": "stdio", "process_execution": {"command": "python mcp/server.py", "working_directory": "/home/<USER>/Documents/TEST_PROJECTS/gitea-mcp-ide", "communication": "JSON-RPC over stdin/stdout"}, "steps": ["1. Launch MCP server process with stdio transport", "2. Initialize MCP session with capabilities exchange", "3. Parse user request for repository and feature information", "4. Use MCP tools to fetch relevant code via JSON-RPC calls", "5. Apply priority file filtering to focus on most relevant files", "6. Build comprehensive context for code understanding"], "key_mcp_tools_for_augment": ["get_repository_info", "list_files", "get_file_content", "search_files"], "stdio_communication_example": {"initialize": {"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "augment-agent", "version": "1.0.0"}}}, "tool_call": {"jsonrpc": "2.0", "id": 2, "method": "tools/call", "params": {"name": "get_file_content", "arguments": {"owner": "Forge", "repo": "maestro", "file_path": "src/main.py", "branch": "main"}}}}}, "automatic_code_retrieval": {"trigger_patterns": ["User mentions specific repository (e.g., 'Forge/maestro')", "User requests features from repos inside Gitea", "User asks for code analysis or implementation help"], "retrieval_strategy": ["Launch stdio MCP server process", "Use priority file list for initial context", "Expand to related files based on dependencies", "Apply intelligent filtering based on user request context", "Cache results for performance optimization"], "stdio_workflow": {"1_process_launch": "python mcp/server.py (stdio mode)", "2_initialization": "Send initialize JSON-RPC message", "3_tool_discovery": "Receive available tools list", "4_code_retrieval": "Call tools via JSON-RPC for file content", "5_context_building": "Aggregate retrieved code for analysis"}}}, "error_handling": {"fallback_mechanisms": ["MCP server unavailable → Direct Gitea API", "Authentication failure → Anonymous access (if allowed)", "Network timeout → Cached content (if available)", "Tool execution failure → Alternative tool or manual retrieval"], "retry_strategies": ["Exponential backoff for network requests", "Alternative authentication methods", "Graceful degradation of functionality"]}, "performance_optimizations": {"caching": {"file_cache_dir": "$HOME/.workflow_cache", "cache_duration": "3600 seconds (configurable)", "cache_strategy": "LRU with size limits"}, "request_optimization": ["Batch file requests when possible", "Prioritize frequently accessed files", "Use streaming for large file transfers", "Implement request deduplication"]}, "augment_stdio_integration": {"description": "Specific configuration for Augment to use gitea-mcp via stdio transport", "mcp_server_command": {"executable": "python", "args": ["mcp/server.py"], "working_directory": "/home/<USER>/Documents/TEST_PROJECTS/gitea-mcp-ide", "environment_variables": {"GITEA_URL": "required", "GITEA_ACCESS_TOKEN": "required", "GITMCP_SERVER_URL": "not_needed_for_stdio", "OPENAI_API_BASE_URL": "not_needed_augment_is_the_model", "OPENAI_API_KEY": "not_needed_augment_is_the_model"}}, "protocol_flow": {"1_launch": "Start MCP server process with stdio", "2_initialize": "Send initialize request with Augment client info", "3_list_tools": "Request available tools from server", "4_call_tools": "Execute repository operations via tool calls", "5_terminate": "Clean shutdown of MCP session"}, "json_rpc_examples": {"list_tools_request": {"jsonrpc": "2.0", "id": 1, "method": "tools/list"}, "get_repo_files": {"jsonrpc": "2.0", "id": 2, "method": "tools/call", "params": {"name": "list_files", "arguments": {"owner": "Forge", "repo": "maestro", "path": "", "branch": "main"}}}, "get_file_content": {"jsonrpc": "2.0", "id": 3, "method": "tools/call", "params": {"name": "get_file_content", "arguments": {"owner": "Forge", "repo": "maestro", "file_path": "src/main.py", "branch": "main"}}}}, "usage_patterns_for_augment": {"feature_request_detection": ["Parse user message for repository references", "Extract owner/repo/branch information", "Identify specific files or components mentioned"], "automatic_code_pulling": ["Launch MCP server for detected repository", "Retrieve priority files first", "Expand to related files based on context", "Build comprehensive code understanding"], "context_optimization": ["Filter files by relevance to user request", "Prioritize recently modified files", "Include configuration and documentation files", "Limit total context size for efficiency"]}}}