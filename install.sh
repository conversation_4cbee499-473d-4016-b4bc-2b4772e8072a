#!/bin/bash
# Cross-platform installation script for Workflow system
# Supports Linux, macOS, and Windows (via Git Bash/WSL)

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Detect operating system
detect_os() {
    case "$(uname -s)" in
        Linux*)     OS=Linux;;
        Darwin*)    OS=Mac;;
        CYGWIN*)    OS=Windows;;
        MINGW*)     OS=Windows;;
        MSYS*)      OS=Windows;;
        *)          OS="Unknown";;
    esac
    print_info "Detected OS: $OS"
}

# Check Python version
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD=python3
    elif command -v python &> /dev/null; then
        PYTHON_CMD=python
    else
        print_error "Python is not installed or not in PATH"
        exit 1
    fi

    # Check Python version
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)

    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
        print_error "Python 3.8+ is required. Found: $PYTHON_VERSION"
        exit 1
    fi

    print_status "Python $PYTHON_VERSION detected"
}

# Check if we're in a virtual environment
check_venv() {
    if [[ -n "$VIRTUAL_ENV" ]] || [[ -n "$CONDA_DEFAULT_ENV" ]]; then
        print_status "Virtual environment detected: ${VIRTUAL_ENV:-$CONDA_DEFAULT_ENV}"
        return 0
    else
        print_warning "No virtual environment detected"
        echo "It's recommended to use a virtual environment."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Installation cancelled. Please create a virtual environment first:"
            print_info "  python3 -m venv venv"
            print_info "  source venv/bin/activate  # Linux/Mac"
            print_info "  # or venv\\Scripts\\activate  # Windows"
            exit 1
        fi
    fi
}

# Install dependencies
install_dependencies() {
    print_info "Installing Python dependencies..."

    if ! $PYTHON_CMD -m pip install -r requirements.txt; then
        print_error "Failed to install dependencies"
        exit 1
    fi

    print_status "Dependencies installed successfully"
}

# Create secure configuration files
create_config() {
    print_info "Creating secure configuration templates..."

    # Run the Python setup script
    if ! $PYTHON_CMD setup.py; then
        print_error "Failed to create configuration files"
        exit 1
    fi

    print_status "Configuration templates created"
}

# Set file permissions (Unix-like systems only)
set_permissions() {
    if [[ "$OS" != "Windows" ]]; then
        print_info "Setting secure file permissions..."

        # Set permissions for local config files if they exist
        if [[ -f "config/local_config.sh" ]]; then
            chmod 600 config/local_config.sh
            print_status "Set permissions for config/local_config.sh"
        fi

        if [[ -f ".env" ]]; then
            chmod 600 .env
            print_status "Set permissions for .env"
        fi
    fi
}

# Main installation function
main() {
    echo "🚀 Workflow System Installation"
    echo "================================"

    detect_os
    check_python
    check_venv
    install_dependencies
    create_config
    set_permissions

    echo ""
    print_status "Installation completed successfully!"
    echo ""
    print_info "Next steps:"
    echo "1. Edit your local configuration file with actual credentials:"
    if [[ "$OS" == "Windows" ]]; then
        echo "   notepad config\\local_config.bat"
    else
        echo "   nano config/local_config.sh"
    fi
    echo ""
    echo "2. Source the configuration:"
    if [[ "$OS" == "Windows" ]]; then
        echo "   config\\local_config.bat"
    else
        echo "   source config/local_config.sh"
    fi
    echo ""
    echo "3. Run the workflow system:"
    echo "   cd workflow"
    echo "   $PYTHON_CMD main.py"
    echo ""
    print_warning "IMPORTANT: Never commit local_config.* files to version control!"
    print_info "See docs/SECURITY.md for detailed security guidelines."
}

# Run main function
main "$@"
