#!/usr/bin/env python3
"""
Cross-platform setup script for Workflow system
"""

import os
import sys
import platform
import subprocess
from pathlib import Path

def run_command(cmd, shell=False):
    """Run a command and return success status"""
    try:
        if platform.system() == "Windows":
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        else:
            result = subprocess.run(cmd, shell=shell, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_git():
    """Check if git is installed"""
    success, _, _ = run_command(["git", "--version"])
    if success:
        print("✅ Git is available")
        return True
    else:
        print("❌ Git is not installed or not in PATH")
        return False

def create_config_file():
    """Create secure config files from templates"""
    import stat

    # Create config directory if it doesn't exist
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)

    # Create local config template for Unix/Linux/Mac
    local_config_sh = config_dir / "local_config.sh"
    if not local_config_sh.exists():
        config_content = '''#!/bin/bash
# Workflow Local Configuration
# This file contains your actual credentials - DO NOT commit to version control

# Gitea Configuration
export GITEA_URL="http://localhost:3000"
export GITEA_ACCESS_TOKEN="your_gitea_token_here"

# MCP Server Configuration
export GITMCP_SERVER_URL="http://localhost:8080"
export GITMCP_API_KEY=""

# OpenAI-Compatible Server Configuration
export OPENAI_API_BASE_URL="http://localhost:8001/v1"
export OPENAI_API_KEY=""

# Local Repository Clone Path
export LOCAL_REPO_CLONE_PATH="/tmp/gitea_clones"

# File Cache Configuration
export FILE_CACHE_DIR="$HOME/.workflow_cache"
export FILE_CACHE_MAX_AGE="3600"

echo "✅ Local workflow environment variables set."
echo "🔗 GITEA_URL=$GITEA_URL"
echo "🔗 GITMCP_SERVER_URL=$GITMCP_SERVER_URL"
echo "🔗 OPENAI_API_BASE_URL=$OPENAI_API_BASE_URL"
'''

        with open(local_config_sh, "w") as f:
            f.write(config_content)

        # Set secure permissions (owner read/write only)
        if platform.system() != "Windows":
            os.chmod(local_config_sh, stat.S_IRUSR | stat.S_IWUSR)

    # Create local config template for Windows
    local_config_bat = config_dir / "local_config.bat"
    if not local_config_bat.exists():
        config_bat = '''@echo off
REM Workflow Local Configuration
REM This file contains your actual credentials - DO NOT commit to version control

REM Gitea Configuration
set GITEA_URL=http://localhost:3000
set GITEA_ACCESS_TOKEN=your_gitea_token_here

REM MCP Server Configuration
set GITMCP_SERVER_URL=http://localhost:8080
set GITMCP_API_KEY=

REM OpenAI-Compatible Server Configuration
set OPENAI_API_BASE_URL=http://localhost:8001/v1
set OPENAI_API_KEY=

REM Local Repository Clone Path
set LOCAL_REPO_CLONE_PATH=%TEMP%\\gitea_clones

REM File Cache Configuration
set FILE_CACHE_DIR=%USERPROFILE%\\.workflow_cache
set FILE_CACHE_MAX_AGE=3600

echo ✅ Local workflow environment variables set.
echo 🔗 GITEA_URL=%GITEA_URL%
echo 🔗 GITMCP_SERVER_URL=%GITMCP_SERVER_URL%
echo 🔗 OPENAI_API_BASE_URL=%OPENAI_API_BASE_URL%
'''

        with open(local_config_bat, "w") as f:
            f.write(config_bat)

    # Create .env template
    env_file = Path(".env.example")
    if not env_file.exists():
        env_content = '''# Workflow Environment Configuration Example
# Copy this file to .env and fill in your actual values
# DO NOT commit .env to version control

GITEA_URL=http://localhost:3000
GITEA_ACCESS_TOKEN=your_gitea_personal_access_token_here
GITMCP_SERVER_URL=http://localhost:8080
OPENAI_API_BASE_URL=http://localhost:8001/v1
LOCAL_REPO_CLONE_PATH=/tmp/gitea_clones
FILE_CACHE_DIR=~/.workflow_cache
FILE_CACHE_MAX_AGE=3600
'''

        with open(env_file, "w") as f:
            f.write(env_content)

    print("✅ Secure configuration templates created:")
    print(f"   📁 {local_config_sh}")
    print(f"   📁 {local_config_bat}")
    print(f"   📁 {env_file}")
    print("")
    print("⚠️  IMPORTANT SECURITY NOTES:")
    print("   • Edit the local_config files with your actual credentials")
    print("   • These files are automatically excluded from version control")
    print("   • Never commit files containing real API keys or tokens")

def install_requirements():
    """Install Python requirements"""
    print("📦 Installing Python dependencies...")

    # Check if we're in a virtual environment
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

    if not in_venv:
        print("⚠️  Warning: Not in a virtual environment")
        response = input("Continue anyway? (y/N): ").lower()
        if response != 'y':
            print("Setup cancelled. Please create a virtual environment first.")
            return False

    success, _, stderr = run_command([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])

    if success:
        print("✅ Dependencies installed successfully")
        return True
    else:
        print(f"❌ Failed to install dependencies: {stderr}")
        return False

def main():
    """Main setup function"""
    print("🚀 Workflow System Setup")
    print("=" * 50)

    # Check prerequisites
    if not check_python_version():
        sys.exit(1)

    if not check_git():
        print("⚠️  Git is recommended but not required")

    # Create config files
    create_config_file()

    # Install requirements
    if not install_requirements():
        sys.exit(1)

    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit config.sh (Linux/Mac) or config.bat (Windows) with your settings")
    print("2. Source the config file:")
    if platform.system() == "Windows":
        print("   Windows: config.bat")
    else:
        print("   Linux/Mac: source config.sh")
    print("3. Run: cd workflow && python main.py")

if __name__ == "__main__":
    main()
