# 🌐 Cross-Platform Compatibility Guide

This guide ensures the Workflow system works seamlessly across Windows, macOS, and Linux.

## 📋 Platform Support Matrix

| Feature | Windows | macOS | Linux | Notes |
|---------|---------|-------|-------|-------|
| Python 3.8+ | ✅ | ✅ | ✅ | Required |
| Virtual Environments | ✅ | ✅ | ✅ | Recommended |
| Configuration Files | ✅ | ✅ | ✅ | Platform-specific templates |
| File Permissions | ⚠️ | ✅ | ✅ | Limited on Windows |
| Path Handling | ✅ | ✅ | ✅ | Cross-platform compatible |
| Environment Variables | ✅ | ✅ | ✅ | Platform-specific syntax |

## 🚀 Installation Methods

### Method 1: Automated Installation (Recommended)

**Linux/macOS:**
```bash
chmod +x install.sh
./install.sh
```

**Windows:**
```cmd
install.bat
```

### Method 2: Manual Installation

**All Platforms:**
```bash
# 1. Install dependencies
python -m pip install -r requirements.txt

# 2. Create configuration
python setup.py

# 3. Configure credentials (see platform-specific sections below)

# 4. Run the system
cd workflow
python main.py
```

## ⚙️ Platform-Specific Configuration

### Windows Configuration

**Create local config:**
```cmd
copy config\config.bat config\local_config.bat
notepad config\local_config.bat
```

**Set environment variables:**
```cmd
config\local_config.bat
```

**File paths use backslashes:**
```cmd
set LOCAL_REPO_CLONE_PATH=%TEMP%\gitea_clones
set FILE_CACHE_DIR=%USERPROFILE%\.workflow_cache
```

### Linux/macOS Configuration

**Create local config:**
```bash
cp config/config.sh config/local_config.sh
nano config/local_config.sh  # or vim, code, etc.
```

**Set environment variables:**
```bash
source config/local_config.sh
```

**File paths use forward slashes:**
```bash
export LOCAL_REPO_CLONE_PATH="/tmp/gitea_clones"
export FILE_CACHE_DIR="$HOME/.workflow_cache"
```

## 🔧 Cross-Platform Development

### Path Handling

The system uses Python's `pathlib` for cross-platform path handling:

```python
from pathlib import Path

# ✅ Cross-platform compatible
config_dir = Path("config")
cache_file = Path.home() / ".workflow_cache" / "data.json"

# ❌ Platform-specific (avoid)
config_dir = "config\\subdir"  # Windows only
cache_file = "/home/<USER>/.cache"  # Unix only
```

### Environment Variables

**Template files handle platform differences:**

```bash
# Linux/macOS (config.sh)
export LOCAL_REPO_CLONE_PATH="${LOCAL_REPO_CLONE_PATH:-/tmp/gitea_clones}"

# Windows (config.bat)
if not defined LOCAL_REPO_CLONE_PATH set LOCAL_REPO_CLONE_PATH=%TEMP%\gitea_clones
```

### File Permissions

**Unix-like systems (Linux/macOS):**
```bash
chmod 600 config/local_config.sh  # Owner read/write only
chmod 700 ~/.workflow_cache        # Owner access only
```

**Windows:**
```cmd
REM Windows file permissions are handled differently
REM Use NTFS permissions or rely on user profile security
```

## 🐍 Python Environment Management

### Virtual Environments

**Create virtual environment:**
```bash
# All platforms
python -m venv venv

# Activate - Linux/macOS
source venv/bin/activate

# Activate - Windows
venv\Scripts\activate
```

### Conda Environments

**Create conda environment:**
```bash
# All platforms
conda create -n workflow python=3.11
conda activate workflow
```

## 🔍 Troubleshooting

### Common Issues

**1. Python not found:**
```bash
# Linux/macOS - try different commands
python3 --version
python --version

# Windows - ensure Python is in PATH
where python
```

**2. Permission denied (Unix):**
```bash
# Make scripts executable
chmod +x install.sh
chmod +x config/local_config.sh
```

**3. Path separator issues:**
```python
# Use pathlib instead of string concatenation
from pathlib import Path
path = Path("config") / "local_config.sh"
```

**4. Environment variables not set:**
```bash
# Linux/macOS - check if sourced
echo $GITEA_URL

# Windows - check if set
echo %GITEA_URL%
```

### Platform-Specific Solutions

**Windows PowerShell execution policy:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**macOS Gatekeeper issues:**
```bash
# If scripts are blocked
xattr -d com.apple.quarantine install.sh
```

**Linux package dependencies:**
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install python3-pip python3-venv

# CentOS/RHEL
sudo yum install python3-pip python3-venv

# Arch Linux
sudo pacman -S python-pip
```

## 📦 Distribution Considerations

### For GitHub Release

**Include platform-specific files:**
- `install.sh` (Linux/macOS)
- `install.bat` (Windows)
- `config/config.sh` (Unix template)
- `config/config.bat` (Windows template)
- `config/env.example` (Cross-platform)

**Archive formats:**
- `.tar.gz` for Linux/macOS
- `.zip` for Windows
- Include both for maximum compatibility

### For Package Managers

**PyPI distribution:**
```python
# setup.py should handle cross-platform installation
python setup.py sdist bdist_wheel
```

**Conda distribution:**
```yaml
# meta.yaml for conda-forge
requirements:
  host:
    - python >=3.8
  run:
    - python >=3.8
    - requests >=2.26.0
```

## ✅ Testing Checklist

Before release, test on each platform:

- [ ] Installation scripts work
- [ ] Configuration files are created correctly
- [ ] Environment variables are set properly
- [ ] File permissions are secure (Unix)
- [ ] Path handling works correctly
- [ ] Dependencies install successfully
- [ ] Main application runs without errors
- [ ] Cross-platform file operations work
- [ ] Network connections function properly
- [ ] Error messages are clear and helpful
