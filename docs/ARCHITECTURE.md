# Workflow System Architecture

## Overview

The Workflow system is an AI-powered development workflow tool that generates structured implementation plans from natural language requests. It integrates with Gitea repositories via the Model Context Protocol (MCP) to provide intelligent, context-aware planning.

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Input    │───▶│  Workflow       │───▶│   Repository    │
│   (Natural      │    │  Trigger        │    │   Context       │
│   Language)     │    │                 │    │   (Gitea-MCP)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Implementation  │◀───│   AI Model      │◀───│   Context       │
│ Plan (JSON)     │    │   Interface     │    │   Builder       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Core Components

### 1. Workflow Trigger (`workflow/trigger.py`)

**Purpose**: Handles user input and workflow initiation.

**Key Features**:
- Interactive project and branch selection
- Input validation and parsing
- User confirmation workflow
- Support for both interactive and programmatic modes

**Methods**:
- `get_user_input()`: Interactive interface for user input
- `submit_issue()`: Programmatic input parsing
- `_get_project_selection()`: Project selection interface
- `_get_branch_selection()`: Branch selection interface
- `_get_user_query()`: User request input
- `_confirm_workflow()`: Workflow confirmation

### 2. Gitea MCP Connector (`workflow/gitea_connector.py`)

**Purpose**: Fetches repository context via Gitea MCP server.

**Key Features**:
- Multiple connection methods (Simplified MCP, LangChain MCP, Direct API)
- Intelligent file caching for performance
- Binary file detection and handling
- Comprehensive error handling and fallbacks

**Connection Hierarchy**:
1. **Simplified MCP Client** (Primary)
2. **LangChain MCP Adapters** (Secondary)
3. **Direct Gitea API** (Fallback)

**Methods**:
- `get_project_repository_context()`: Main context fetching method
- `get_mcp_tools()`: Retrieve MCP tools for AI model
- `_init_langchain_mcp_client()`: Initialize LangChain MCP connection
- `_direct_http_fallback()`: Direct API fallback method

### 3. Context Builder (`workflow/context_builder.py`)

**Purpose**: Combines user input with repository context for AI processing.

**Key Features**:
- Context optimization and token management
- File prioritization based on relevance
- Content truncation for large repositories
- Structured context formatting

**Methods**:
- `build_combined_context()`: Main context building method
- `_optimize_context()`: Context optimization for token limits
- `_prioritize_files()`: File importance ranking
- `_truncate_content()`: Content size management

### 4. AI Model Interface (`workflow/model_interface.py`)

**Purpose**: Interfaces with AI models to generate implementation plans.

**Key Features**:
- Support for OpenAI-compatible APIs
- LangChain integration with MCP tools
- Multiple model backends (ChatOpenAI, VLLMOpenAI)
- Structured JSON output generation

**Methods**:
- `generate_llm_template_and_send()`: Main plan generation method
- `set_mcp_tools()`: Configure MCP tools for model
- `_direct_openai_call()`: Direct API call method
- `_create_system_prompt()`: System prompt generation

## MCP (Model Context Protocol) Components

### 1. MCP Server (`mcp/server.py`)

**Purpose**: Provides MCP-compatible interface to Gitea repositories.

**Key Features**:
- RESTful API with MCP protocol compliance
- Server-Sent Events (SSE) support
- Multiple authentication methods
- Tool-based repository operations

**Endpoints**:
- `/version`: Server version information
- `/`: Main MCP endpoint (POST)
- SSE streaming support for real-time communication

**Tools**:
- `get_file_content`: Retrieve file contents
- `list_branches`: List repository branches
- `list_files`: List repository files

### 2. MCP Client (`mcp/client.py`)

**Purpose**: Simplified client for MCP server communication.

**Key Features**:
- Direct Gitea API integration
- Simple request/response interface
- Error handling and validation
- Repository operation abstractions

**Methods**:
- `get_repository_info()`: Repository metadata
- `list_files()`: Directory listing
- `get_file_content()`: File content retrieval
- `list_branches()`: Branch listing

## Utility Components

### 1. File Cache (`utils/file_cache.py`)

**Purpose**: Intelligent caching system for repository files.

**Key Features**:
- SHA-based cache validation
- Automatic cache expiration
- Size-based cache management
- Performance statistics

### 2. Context Optimizer (`utils/context_optimizer.py`)

**Purpose**: Optimizes context for AI model consumption.

**Key Features**:
- Token counting and management
- Content prioritization
- Intelligent truncation
- Context quality metrics

### 3. Banner Utility (`utils/banner.py`)

**Purpose**: Provides visual feedback and branding.

**Key Features**:
- ASCII art banners
- System status display
- Progress indicators
- User-friendly messaging

## Data Flow

### 1. Input Processing
```
User Input → Validation → Project/Branch Selection → Confirmation
```

### 2. Context Gathering
```
Repository ID → MCP Server → File List → Content Retrieval → Caching
```

### 3. Context Building
```
User Input + Repository Context → Optimization → Token Management → AI-Ready Context
```

### 4. Plan Generation
```
Combined Context → AI Model → Structured Plan → JSON Output → File Storage
```

## Configuration

### Environment Variables

- `GITEA_URL`: Gitea instance URL
- `GITEA_ACCESS_TOKEN`: Personal access token
- `GITMCP_SERVER_URL`: MCP server endpoint
- `OPENAI_API_BASE_URL`: AI model API endpoint
- `OPENAI_API_KEY`: API authentication key
- `OPENAI_MODEL_NAME`: Model identifier

### Configuration Files

- `config/config.sh`: Linux/Mac configuration
- `config/config.bat`: Windows configuration

## Error Handling

### Graceful Degradation
1. **MCP Connection Failure**: Falls back to direct API
2. **Authentication Issues**: Attempts multiple auth methods
3. **Network Timeouts**: Implements retry logic
4. **Large Repositories**: Applies intelligent truncation

### Error Recovery
- Automatic fallback mechanisms
- Detailed error logging
- User-friendly error messages
- Graceful shutdown procedures

## Performance Optimizations

### Caching Strategy
- File-based caching with SHA validation
- Intelligent cache expiration
- Memory-efficient storage
- Cache statistics and monitoring

### Context Optimization
- Token-aware content truncation
- File prioritization algorithms
- Lazy loading of repository content
- Efficient data structures

### Network Efficiency
- Connection pooling
- Request batching
- Timeout management
- Retry mechanisms

## Security Considerations

### Authentication
- Multiple authentication methods
- Secure token storage
- API key management
- Access control validation

### Data Protection
- No sensitive data logging
- Secure communication channels
- Input validation and sanitization
- Error message sanitization

## Extensibility

### Plugin Architecture
- Modular component design
- Interface-based abstractions
- Configuration-driven behavior
- Easy component replacement

### Integration Points
- Custom MCP servers
- Alternative AI models
- Different repository systems
- Custom context builders

## Testing Strategy

### Unit Tests
- Component isolation testing
- Mock-based testing
- Edge case validation
- Error condition testing

### Integration Tests
- End-to-end workflow testing
- MCP server integration
- AI model integration
- Configuration validation

### Performance Tests
- Load testing
- Memory usage monitoring
- Response time measurement
- Cache efficiency testing

## Deployment

### Local Development
```bash
# Clone repository
git clone <repository-url>
cd workflow

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp config/config.sh.example config/config.sh
# Edit config/config.sh with your settings

# Start MCP server
python mcp/server.py &

# Run workflow
python main.py
```

### Production Deployment
- Docker containerization support
- Environment-based configuration
- Health check endpoints
- Monitoring and logging integration
