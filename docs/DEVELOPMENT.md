# Development Guide

## Getting Started

### Prerequisites

- Python 3.8 or higher
- Git
- Access to a Gitea instance
- OpenAI-compatible LLM server

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd workflow
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # or
   venv\Scripts\activate     # Windows
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt  # Development dependencies
   ```

4. **Configure environment**
   ```bash
   cp config/config.sh.example config/config.sh
   # Edit config/config.sh with your settings
   source config/config.sh
   ```

5. **Start MCP server**
   ```bash
   python mcp/server.py
   ```

6. **Run the workflow**
   ```bash
   python main.py
   ```

## Project Structure

```
workflow/
├── main.py                 # Main entry point
├── workflow/               # Core workflow components
│   ├── __init__.py
│   ├── trigger.py         # User input handling
│   ├── gitea_connector.py # Repository context fetching
│   ├── context_builder.py # Context combination
│   └── model_interface.py # AI model interface
├── mcp/                   # MCP server and client
│   ├── __init__.py
│   ├── server.py         # Gitea MCP server
│   └── client.py         # Simplified MCP client
├── utils/                 # Utility modules
│   ├── __init__.py
│   ├── banner.py         # UI utilities
│   ├── context_optimizer.py
│   └── file_cache.py     # Caching system
├── config/               # Configuration files
│   ├── config.sh         # Linux/Mac config
│   └── config.bat        # Windows config
├── docs/                 # Documentation
├── examples/             # Example files
├── tests/                # Test suite
├── .github/              # GitHub workflows
├── requirements.txt      # Dependencies
├── setup.py             # Package setup
├── .gitignore           # Git ignore rules
├── LICENSE              # MIT License
└── README.md            # Project overview
```

## Development Workflow

### 1. Feature Development

1. **Create feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Implement changes**
   - Follow the existing code style
   - Add appropriate tests
   - Update documentation

3. **Run tests**
   ```bash
   pytest tests/
   ```

4. **Check code quality**
   ```bash
   black .                    # Format code
   flake8 .                   # Lint code
   mypy workflow/ mcp/        # Type checking
   ```

5. **Commit and push**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   git push origin feature/your-feature-name
   ```

### 2. Testing

#### Unit Tests
```bash
# Run all tests
pytest tests/

# Run specific test file
pytest tests/test_trigger.py

# Run with coverage
pytest tests/ --cov=workflow --cov=mcp
```

#### Integration Tests
```bash
# Test MCP server
python mcp/server.py &
curl http://localhost:8080/version

# Test workflow end-to-end
python main.py
```

#### Manual Testing
```bash
# Test individual components
python workflow/trigger.py
python workflow/gitea_connector.py
python mcp/client.py
```

### 3. Code Quality

#### Formatting
```bash
# Format all Python files
black .

# Check formatting
black --check .
```

#### Linting
```bash
# Lint all files
flake8 .

# Lint specific directory
flake8 workflow/
```

#### Type Checking
```bash
# Type check core modules
mypy workflow/ mcp/ --ignore-missing-imports
```

## Adding New Features

### 1. New Workflow Component

1. Create new module in `workflow/` directory
2. Implement the component class
3. Add to `workflow/__init__.py`
4. Update `main.py` to use the component
5. Add tests in `tests/`
6. Update documentation

### 2. New MCP Tool

1. Add tool definition to `mcp/server.py`
2. Implement tool execution logic
3. Update tool list in `get_tools()` method
4. Test with MCP client
5. Add integration tests

### 3. New Utility

1. Create module in `utils/` directory
2. Implement utility functions
3. Add to `utils/__init__.py`
4. Add unit tests
5. Update documentation

## Configuration Management

### Environment Variables

Required variables:
- `GITEA_URL`: Gitea instance URL
- `GITEA_ACCESS_TOKEN`: Personal access token
- `GITMCP_SERVER_URL`: MCP server URL
- `OPENAI_API_BASE_URL`: AI model API URL

Optional variables:
- `OPENAI_API_KEY`: API key (may be dummy for local models)
- `OPENAI_MODEL_NAME`: Specific model to use
- `FILE_CACHE_DIR`: Cache directory location
- `FILE_CACHE_MAX_AGE`: Cache expiration time

### Configuration Files

- `config/config.sh`: Linux/Mac environment setup
- `config/config.bat`: Windows environment setup

## Debugging

### Enable Debug Logging
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### MCP Server Debugging
```bash
# Start server with debug logging
PYTHONPATH=. python mcp/server.py
```

### Component Testing
```bash
# Test individual components
python -c "from workflow.trigger import WorkflowTrigger; t = WorkflowTrigger(); print(t.get_user_input())"
```

## Performance Optimization

### Profiling
```bash
# Profile main workflow
python -m cProfile -o profile.stats main.py

# Analyze profile
python -c "import pstats; p = pstats.Stats('profile.stats'); p.sort_stats('cumulative').print_stats(10)"
```

### Memory Usage
```bash
# Monitor memory usage
python -m memory_profiler main.py
```

### Cache Performance
```python
# Check cache statistics
from utils.file_cache import FileCache
cache = FileCache()
stats = cache.get_stats()
print(f"Cache efficiency: {stats}")
```

## Contributing Guidelines

### Code Style
- Follow PEP 8 style guidelines
- Use Black for code formatting
- Maximum line length: 88 characters
- Use type hints where appropriate

### Commit Messages
- Use conventional commit format
- Examples:
  - `feat: add new MCP tool for file operations`
  - `fix: resolve cache invalidation issue`
  - `docs: update API documentation`
  - `test: add integration tests for MCP server`

### Pull Requests
- Create feature branches from `main`
- Include tests for new functionality
- Update documentation as needed
- Ensure CI passes before merging

### Documentation
- Update README.md for user-facing changes
- Update ARCHITECTURE.md for system changes
- Add docstrings to all public methods
- Include examples for new features

## Troubleshooting

### Common Issues

1. **MCP Server Connection Failed**
   - Check if server is running: `curl http://localhost:8080/version`
   - Verify GITMCP_SERVER_URL environment variable
   - Check firewall settings

2. **Gitea Authentication Failed**
   - Verify GITEA_ACCESS_TOKEN is valid
   - Check token permissions in Gitea
   - Try username/password authentication

3. **AI Model Connection Failed**
   - Verify OPENAI_API_BASE_URL is correct
   - Check if model server is running
   - Validate API key if required

4. **Import Errors**
   - Ensure virtual environment is activated
   - Install missing dependencies: `pip install -r requirements.txt`
   - Check Python path configuration

### Getting Help

- Check existing issues on GitHub
- Review documentation in `docs/` directory
- Run component tests to isolate problems
- Enable debug logging for detailed information
