# 🔒 Security Guide

This document outlines security best practices for the Workflow system.

## 🚨 Critical Security Requirements

### 1. Environment Configuration

**NEVER commit sensitive data to version control:**

- ✅ Use `config/local_config.sh` or `config/local_config.bat` for local settings
- ✅ Use `.env` files for environment variables
- ❌ DO NOT edit `config/config.sh` or `config/config.bat` with real credentials
- ❌ DO NOT commit files containing API keys, tokens, or passwords

### 2. API Token Security

**Gitea Personal Access Token:**
```bash
# Generate in Gitea: Settings > Applications > Generate New Token
# Minimum required permissions:
# - repository:read
# - user:read (if accessing private repos)
export GITEA_ACCESS_TOKEN="gitea_xxxxxxxxxxxxxxxxxx"
```

**OpenAI API Key (if required):**
```bash
# Only needed if your OpenAI-compatible server requires authentication
export OPENAI_API_KEY="sk-xxxxxxxxxxxxxxxxxx"
```

### 3. Network Security

**Validate SSL certificates:**
```bash
export VALIDATE_SSL=true
```

**Use secure URLs when possible:**
```bash
# Prefer HTTPS over HTTP
export GITEA_URL="https://your-gitea-instance.com"
export OPENAI_API_BASE_URL="https://your-llm-server.com/v1"
```

### 4. File System Security

**Secure cache directory:**
```bash
# Use user-specific cache directory
export FILE_CACHE_DIR="$HOME/.workflow_cache"
chmod 700 "$HOME/.workflow_cache"  # Owner read/write/execute only
```

**Temporary files:**
```bash
# Ensure temp directory is secure
export LOCAL_REPO_CLONE_PATH="/tmp/gitea_clones"
```

## 🛡️ Security Checklist

Before deploying or sharing:

- [ ] All sensitive data moved to local config files
- [ ] `.gitignore` properly configured
- [ ] No hardcoded credentials in source code
- [ ] SSL validation enabled
- [ ] Secure file permissions set
- [ ] API tokens have minimal required permissions
- [ ] Network endpoints use HTTPS when possible
- [ ] Log files don't contain sensitive data

## 🔧 Secure Setup Process

### 1. Initial Setup
```bash
# Clone repository
git clone https://github.com/your-org/workflow.git
cd workflow

# Copy configuration templates
cp config/config.sh config/local_config.sh
cp config/env.example .env

# Set secure permissions
chmod 600 config/local_config.sh
chmod 600 .env
```

### 2. Configure Credentials
```bash
# Edit local configuration (never commit this file)
nano config/local_config.sh

# Or use environment file
nano .env
```

### 3. Verify Security
```bash
# Check that sensitive files are ignored
git status
# Should NOT show local_config.sh, .env, or any credential files

# Verify file permissions
ls -la config/local_config.sh  # Should show -rw-------
ls -la .env                    # Should show -rw-------
```

## 🚫 What NOT to Do

- ❌ Never commit real API keys or tokens
- ❌ Never use production credentials in development
- ❌ Never share configuration files containing secrets
- ❌ Never disable SSL validation in production
- ❌ Never use default passwords or tokens
- ❌ Never store credentials in plain text files in the repository

## 🆘 Security Incident Response

If credentials are accidentally committed:

1. **Immediately revoke the exposed credentials**
2. **Generate new API keys/tokens**
3. **Remove sensitive data from git history:**
   ```bash
   git filter-branch --force --index-filter \
   'git rm --cached --ignore-unmatch path/to/sensitive/file' \
   --prune-empty --tag-name-filter cat -- --all
   ```
4. **Force push to remote (if you have permission)**
5. **Notify team members to re-clone the repository**

## 📞 Reporting Security Issues

If you discover a security vulnerability:

1. **DO NOT** create a public issue
2. Email security concerns to: [<EMAIL>]
3. Include detailed description and reproduction steps
4. Allow reasonable time for response before public disclosure

## 🔄 Regular Security Maintenance

- Review and rotate API keys quarterly
- Update dependencies regularly
- Monitor access logs for unusual activity
- Audit file permissions periodically
- Review `.gitignore` when adding new file types
