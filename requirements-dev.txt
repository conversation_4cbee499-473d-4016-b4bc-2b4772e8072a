# Development dependencies for the workflow project

# Include production dependencies
-r requirements.txt

# Testing framework
pytest>=6.0.0
pytest-cov>=2.0.0
pytest-mock>=3.0.0
pytest-asyncio>=0.18.0

# Code quality and formatting
black>=22.0.0
flake8>=4.0.0
isort>=5.0.0
mypy>=0.900

# Documentation
sphinx>=4.0.0
sphinx-rtd-theme>=1.0.0
myst-parser>=0.17.0

# Performance and profiling
memory-profiler>=0.60.0
psutil>=5.8.0
line-profiler>=3.0.0

# Development utilities
ipython>=7.0.0
jupyter>=1.0.0
pre-commit>=2.15.0

# HTTP testing
httpx>=0.23.0
responses>=0.18.0

# Async testing
aioresponses>=0.7.0

# Security scanning
bandit>=1.7.0
safety>=2.0.0
