@echo off
REM Cross-platform installation script for Workflow system (Windows)
setlocal enabledelayedexpansion

echo 🚀 Workflow System Installation (Windows)
echo ========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python %PYTHON_VERSION% detected

REM Check Python version (basic check)
for /f "tokens=1,2 delims=." %%a in ("%PYTHON_VERSION%") do (
    set MAJOR=%%a
    set MINOR=%%b
)

if %MAJOR% LSS 3 (
    echo ❌ Python 3.8+ is required. Found: %PYTHON_VERSION%
    pause
    exit /b 1
)

if %MAJOR% EQU 3 if %MINOR% LSS 8 (
    echo ❌ Python 3.8+ is required. Found: %PYTHON_VERSION%
    pause
    exit /b 1
)

REM Check for virtual environment
if defined VIRTUAL_ENV (
    echo ✅ Virtual environment detected: %VIRTUAL_ENV%
) else if defined CONDA_DEFAULT_ENV (
    echo ✅ Conda environment detected: %CONDA_DEFAULT_ENV%
) else (
    echo ⚠️  No virtual environment detected
    echo It's recommended to use a virtual environment.
    set /p CONTINUE="Continue anyway? (y/N): "
    if /i not "!CONTINUE!"=="y" (
        echo Installation cancelled. Please create a virtual environment first:
        echo   python -m venv venv
        echo   venv\Scripts\activate
        pause
        exit /b 1
    )
)

REM Install dependencies
echo ℹ️  Installing Python dependencies...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)
echo ✅ Dependencies installed successfully

REM Create configuration files
echo ℹ️  Creating secure configuration templates...
python setup.py
if errorlevel 1 (
    echo ❌ Failed to create configuration files
    pause
    exit /b 1
)
echo ✅ Configuration templates created

echo.
echo ✅ Installation completed successfully!
echo.
echo ℹ️  Next steps:
echo 1. Edit your local configuration file with actual credentials:
echo    notepad config\local_config.bat
echo.
echo 2. Source the configuration:
echo    config\local_config.bat
echo.
echo 3. Run the workflow system:
echo    cd workflow
echo    python main.py
echo.
echo ⚠️  IMPORTANT: Never commit local_config.* files to version control!
echo ℹ️  See docs\SECURITY.md for detailed security guidelines.
echo.
pause
