"""
Tests for the WorkflowTrigger component.
"""

import pytest
from unittest.mock import patch, MagicMock
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from workflow.trigger import WorkflowTrigger


class TestWorkflowTrigger:
    """Test cases for WorkflowTrigger."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.trigger = WorkflowTrigger()
    
    def test_submit_issue_valid_input(self):
        """Test parsing valid input string."""
        input_string = "Forge/maestro,main,Add dark mode toggle"
        result = self.trigger.submit_issue(input_string)
        
        assert result is not None
        assert result == ("Forge/maestro", "main", "Add dark mode toggle")
    
    def test_submit_issue_invalid_format(self):
        """Test parsing invalid input format."""
        input_string = "InvalidFormat"
        result = self.trigger.submit_issue(input_string)
        
        assert result is None
    
    def test_submit_issue_missing_parts(self):
        """Test parsing input with missing parts."""
        input_string = "Forge/maestro,main,"
        result = self.trigger.submit_issue(input_string)
        
        assert result is None
    
    def test_submit_issue_empty_parts(self):
        """Test parsing input with empty parts."""
        input_string = ",main,Add feature"
        result = self.trigger.submit_issue(input_string)
        
        assert result is None
    
    @patch('builtins.input')
    def test_get_project_selection_common_project(self, mock_input):
        """Test selecting a common project."""
        mock_input.return_value = "1"
        
        result = self.trigger._get_project_selection()
        
        assert result == "Forge/maestro"
    
    @patch('builtins.input')
    def test_get_project_selection_custom_project(self, mock_input):
        """Test entering a custom project."""
        mock_input.side_effect = ["3", "Custom/Project"]
        
        result = self.trigger._get_project_selection()
        
        assert result == "Custom/Project"
    
    @patch('builtins.input')
    def test_get_branch_selection_common_branch(self, mock_input):
        """Test selecting a common branch."""
        mock_input.return_value = "1"
        
        result = self.trigger._get_branch_selection()
        
        assert result == "main"
    
    @patch('builtins.input')
    def test_get_user_query_valid(self, mock_input):
        """Test entering a valid user query."""
        mock_input.return_value = "Add dark mode toggle"
        
        result = self.trigger._get_user_query()
        
        assert result == "Add dark mode toggle"
    
    @patch('builtins.input')
    def test_get_user_query_empty(self, mock_input):
        """Test entering an empty user query."""
        mock_input.return_value = ""
        
        result = self.trigger._get_user_query()
        
        assert result is None
    
    @patch('builtins.input')
    def test_confirm_workflow_yes(self, mock_input):
        """Test confirming workflow execution."""
        mock_input.return_value = "y"
        
        result = self.trigger._confirm_workflow("Forge/maestro", "main", "Add feature")
        
        assert result is True
    
    @patch('builtins.input')
    def test_confirm_workflow_no(self, mock_input):
        """Test declining workflow execution."""
        mock_input.return_value = "n"
        
        result = self.trigger._confirm_workflow("Forge/maestro", "main", "Add feature")
        
        assert result is False


if __name__ == "__main__":
    pytest.main([__file__])
