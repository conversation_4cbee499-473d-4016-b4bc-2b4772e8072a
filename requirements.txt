# Python dependencies for the workflow project

# Git operations
GitPython>=3.1.0

# HTTP requests (for API interactions)
requests>=2.26.0

# Environment variable management
python-dotenv>=0.19.0

# JSON handling
pydantic>=1.8.0

# LangChain and MCP integration
langchain>=0.0.267
langchain-openai>=0.0.1
langchain-core>=0.0.1
langchain-community>=0.0.1  # Community integrations (VLLMOpenAI)
langchain-mcp-adapters>=0.0.1  # Required for MCP client functionality
mcp>=0.0.1  # Base MCP package
python-multipart>=0.0.6  # Required for multipart requests

# Async HTTP server (for MCP server)
aiohttp>=3.8.0

# Gitea-MCP specific packages
# Note: gitea-mcp server should be installed separately if needed
# This project includes its own MCP client implementation
