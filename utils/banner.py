#!/usr/bin/env python3
"""
Cool banner display for Workflow system
"""

import os
import platform

def get_terminal_width():
    """Get terminal width, default to 80 if unable to determine"""
    try:
        return os.get_terminal_size().columns
    except:
        return 80

def display_banner():
    """Display the cool Workflow banner"""
    width = min(get_terminal_width(), 100)  # Max width of 100

    # Colors for different platforms
    if platform.system() == "Windows":
        # Windows CMD colors
        BLUE = ""
        CYAN = ""
        GREEN = ""
        YELLOW = ""
        RESET = ""
        try:
            # Try to enable ANSI colors on Windows 10+
            os.system('color')
            BLUE = "\033[94m"
            CYAN = "\033[96m"
            GREEN = "\033[92m"
            YELLOW = "\033[93m"
            RESET = "\033[0m"
        except:
            pass
    else:
        # Unix/Linux ANSI colors
        BLUE = "\033[94m"
        CYAN = "\033[96m"
        GREEN = "\033[92m"
        YELLOW = "\033[93m"
        RESET = "\033[0m"

    banner = f"""
{CYAN}╔{'═' * (width - 2)}╗{RESET}
{CYAN}║{' ' * (width - 2)}║{RESET}
{CYAN}║{BLUE}    ██╗    ██╗ ██████╗ ██████╗ ██╗  ██╗███████╗██╗      ██████╗ ██╗    ██╗{' ' * (width - 76)}{CYAN}║{RESET}
{CYAN}║{BLUE}    ██║    ██║██╔═══██╗██╔══██╗██║ ██╔╝██╔════╝██║     ██╔═══██╗██║    ██║{' ' * (width - 76)}{CYAN}║{RESET}
{CYAN}║{BLUE}    ██║ █╗ ██║██║   ██║██████╔╝█████╔╝ █████╗  ██║     ██║   ██║██║ █╗ ██║{' ' * (width - 76)}{CYAN}║{RESET}
{CYAN}║{BLUE}    ██║███╗██║██║   ██║██╔══██╗██╔═██╗ ██╔══╝  ██║     ██║   ██║██║███╗██║{' ' * (width - 76)}{CYAN}║{RESET}
{CYAN}║{BLUE}    ╚███╔███╔╝╚██████╔╝██║  ██║██║  ██╗██║     ███████╗╚██████╔╝╚███╔███╔╝{' ' * (width - 76)}{CYAN}║{RESET}
{CYAN}║{BLUE}     ╚══╝╚══╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝     ╚══════╝ ╚═════╝  ╚══╝╚══╝{' ' * (width - 75)}{CYAN}║{RESET}
{CYAN}║{' ' * (width - 2)}║{RESET}
{CYAN}║{GREEN}    🚀 AI-Powered Development Workflow System{' ' * (width - 47)}{CYAN}║{RESET}
{CYAN}║{YELLOW}    📋 Generate structured implementation plans from natural language{' ' * (width - 71)}{CYAN}║{RESET}
{CYAN}║{' ' * (width - 2)}║{RESET}
{CYAN}╚{'═' * (width - 2)}╝{RESET}
"""

    print(banner)

def display_startup_info():
    """Display startup information"""
    CYAN = "\033[96m"
    GREEN = "\033[92m"
    YELLOW = "\033[93m"
    RESET = "\033[0m"

    if platform.system() == "Windows":
        try:
            os.system('color')
        except:
            CYAN = GREEN = YELLOW = RESET = ""

    print(f"{GREEN}✓{RESET} System initialized successfully")
    print(f"{CYAN}ℹ{RESET} Interactive workflow client ready")
    print()

if __name__ == "__main__":
    display_banner()
    display_startup_info()
