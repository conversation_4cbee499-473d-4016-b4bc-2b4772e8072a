#!/usr/bin/env python3
"""
Utility module for optimizing context for LLM token usage.
"""

import os
import re
import logging
from typing import Dict, List, Any, Optional, Tuple, Set

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("ContextOptimizer")

# Approximate token counts for different languages
# These are rough estimates and may vary depending on the tokenizer
TOKEN_MULTIPLIERS = {
    "py": 0.8,    # Python tends to be more token-efficient
    "js": 0.9,    # JavaScript
    "ts": 0.9,    # TypeScript
    "go": 0.85,   # Go
    "java": 1.0,  # Java tends to be more verbose
    "rb": 0.75,   # Ruby tends to be more concise
    "php": 0.95,  # PHP
    "cs": 1.0,    # C#
    "cpp": 0.9,   # C++
    "c": 0.85,    # C
    "rs": 0.9,    # Rust
    "md": 0.7,    # Markdown
    "txt": 0.7,   # Plain text
    "json": 1.1,  # JSON tends to be more verbose
    "yaml": 0.8,  # YAML
    "yml": 0.8,   # YAML
    "html": 1.2,  # HTML tends to be very verbose
    "css": 1.0,   # CSS
    "scss": 1.0,  # SCSS
    "sh": 0.75,   # Shell scripts
    "default": 0.9  # Default multiplier
}

# Maximum tokens per file (to avoid exceeding LLM context limits)
MAX_TOKENS_PER_FILE = 1500

# Maximum tokens for the entire context
MAX_TOTAL_TOKENS = 6000

class ContextOptimizer:
    """
    Utility class for optimizing context for LLM token usage.
    """
    
    @staticmethod
    def estimate_tokens(text: str, file_extension: str = None) -> int:
        """
        Estimate the number of tokens in a text string.
        
        Args:
            text: The text to estimate tokens for.
            file_extension: The file extension to use for token multiplier.
            
        Returns:
            The estimated number of tokens.
        """
        if not text:
            return 0
            
        # Get the multiplier based on file extension
        multiplier = TOKEN_MULTIPLIERS.get("default", 0.9)
        if file_extension:
            ext = file_extension.lstrip('.').lower()
            multiplier = TOKEN_MULTIPLIERS.get(ext, multiplier)
            
        # Estimate tokens (rough approximation: 4 characters per token)
        return int(len(text) / 4 * multiplier)
    
    @staticmethod
    def truncate_file_content(content: str, file_path: str, max_tokens: int = MAX_TOKENS_PER_FILE) -> str:
        """
        Truncate file content to fit within token limits while preserving important sections.
        
        Args:
            content: The file content to truncate.
            file_path: The path to the file (used to determine file type).
            max_tokens: The maximum number of tokens to allow.
            
        Returns:
            The truncated file content.
        """
        if not content:
            return ""
            
        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lstrip('.').lower() if ext else ""
        
        # Estimate current tokens
        estimated_tokens = ContextOptimizer.estimate_tokens(content, ext)
        
        # If already within limits, return as is
        if estimated_tokens <= max_tokens:
            return content
            
        # For very large files, we need to be more aggressive
        if estimated_tokens > max_tokens * 3:
            logger.info(f"File {file_path} is very large ({estimated_tokens} tokens). Using aggressive truncation.")
            return ContextOptimizer._aggressive_truncate(content, file_path, max_tokens)
            
        # Otherwise, use smart truncation based on file type
        if ext in ["py", "js", "ts", "go", "java", "rb", "php", "cs", "cpp", "c", "rs"]:
            return ContextOptimizer._truncate_code_file(content, file_path, max_tokens)
        elif ext in ["md", "txt"]:
            return ContextOptimizer._truncate_text_file(content, file_path, max_tokens)
        elif ext in ["json", "yaml", "yml"]:
            return ContextOptimizer._truncate_data_file(content, file_path, max_tokens)
        else:
            # Default truncation for unknown file types
            return ContextOptimizer._default_truncate(content, file_path, max_tokens)
    
    @staticmethod
    def _aggressive_truncate(content: str, file_path: str, max_tokens: int) -> str:
        """
        Aggressively truncate a file by keeping only the most important parts.
        
        Args:
            content: The file content to truncate.
            file_path: The path to the file.
            max_tokens: The maximum number of tokens to allow.
            
        Returns:
            The truncated file content.
        """
        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lstrip('.').lower() if ext else ""
        
        # Split the content into lines
        lines = content.split('\n')
        
        # Keep the first 20% and last 10% of lines
        total_lines = len(lines)
        head_lines = int(total_lines * 0.2)
        tail_lines = int(total_lines * 0.1)
        
        # Ensure we have at least some lines
        head_lines = max(head_lines, 10)
        tail_lines = max(tail_lines, 5)
        
        # Get the head and tail sections
        head = '\n'.join(lines[:head_lines])
        tail = '\n'.join(lines[-tail_lines:])
        
        # Combine with a note about truncation
        truncated = f"{head}\n\n[...file truncated due to size...]\n\n{tail}"
        
        # If still too large, fall back to even more aggressive truncation
        if ContextOptimizer.estimate_tokens(truncated, ext) > max_tokens:
            # Just keep the first few lines with a note
            truncated = '\n'.join(lines[:int(max_tokens / 2 * 4 / TOKEN_MULTIPLIERS.get(ext, 0.9))])
            truncated += f"\n\n[...remainder of file truncated ({total_lines - int(max_tokens / 2 * 4 / TOKEN_MULTIPLIERS.get(ext, 0.9))} lines)...]"
        
        return truncated
    
    @staticmethod
    def _truncate_code_file(content: str, file_path: str, max_tokens: int) -> str:
        """
        Truncate a code file while preserving important sections like imports, class/function definitions.
        
        Args:
            content: The file content to truncate.
            file_path: The path to the file.
            max_tokens: The maximum number of tokens to allow.
            
        Returns:
            The truncated file content.
        """
        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lstrip('.').lower() if ext else ""
        
        # Split the content into lines
        lines = content.split('\n')
        
        # Identify important lines (imports, class/function definitions, etc.)
        important_line_indices = []
        
        # Regular expressions for important patterns in different languages
        import_patterns = {
            "py": r"^\s*(import|from)\s+.+",
            "js": r"^\s*(import|require|export)\s+.+",
            "ts": r"^\s*(import|require|export)\s+.+",
            "go": r"^\s*(import|package)\s+.+",
            "java": r"^\s*(import|package)\s+.+",
            "rb": r"^\s*(require|include|load|extend)\s+.+",
            "php": r"^\s*(require|include|use|namespace)\s+.+",
            "cs": r"^\s*(using|namespace)\s+.+",
            "cpp": r"^\s*(#include|using|namespace)\s+.+",
            "c": r"^\s*#include\s+.+",
            "rs": r"^\s*(use|mod|extern crate)\s+.+"
        }
        
        definition_patterns = {
            "py": r"^\s*(def|class|async def)\s+.+",
            "js": r"^\s*(function|class|const|let|var|async function)\s+.+",
            "ts": r"^\s*(function|class|interface|type|const|let|var|async function)\s+.+",
            "go": r"^\s*(func|type|struct|interface)\s+.+",
            "java": r"^\s*(class|interface|enum|public|private|protected)\s+.+",
            "rb": r"^\s*(def|class|module|attr_accessor)\s+.+",
            "php": r"^\s*(function|class|interface|trait)\s+.+",
            "cs": r"^\s*(class|interface|enum|struct|public|private|protected)\s+.+",
            "cpp": r"^\s*(class|struct|enum|template|namespace)\s+.+",
            "c": r"^\s*(struct|enum|typedef|void|int|char|float|double)\s+.+\(.+\)",
            "rs": r"^\s*(fn|struct|enum|trait|impl|pub)\s+.+"
        }
        
        # Get the appropriate patterns for this file type
        import_pattern = import_patterns.get(ext, r"^\s*(import|require|using|include)\s+.+")
        definition_pattern = definition_patterns.get(ext, r"^\s*(function|class|def|struct|interface)\s+.+")
        
        # Find important lines
        for i, line in enumerate(lines):
            # Check for imports
            if re.match(import_pattern, line):
                important_line_indices.append(i)
            
            # Check for definitions
            if re.match(definition_pattern, line):
                important_line_indices.append(i)
                
                # Also include the next few lines (function/class signature)
                for j in range(1, 4):
                    if i + j < len(lines):
                        important_line_indices.append(i + j)
        
        # Always include the first 10 lines (likely to contain important context)
        for i in range(min(10, len(lines))):
            if i not in important_line_indices:
                important_line_indices.append(i)
        
        # Sort the indices
        important_line_indices = sorted(important_line_indices)
        
        # Build the truncated content
        truncated_lines = []
        last_included = -2
        
        for i in important_line_indices:
            # If there's a gap, add a note
            if i > last_included + 1:
                truncated_lines.append(f"[...{i - last_included - 1} lines truncated...]")
            
            # Add the important line
            truncated_lines.append(lines[i])
            last_included = i
        
        # If we didn't include the last line, add a note
        if last_included < len(lines) - 1:
            truncated_lines.append(f"[...{len(lines) - last_included - 1} lines truncated...]")
        
        # Join the lines
        truncated = '\n'.join(truncated_lines)
        
        # If still too large, fall back to aggressive truncation
        if ContextOptimizer.estimate_tokens(truncated, ext) > max_tokens:
            return ContextOptimizer._aggressive_truncate(content, file_path, max_tokens)
        
        return truncated
    
    @staticmethod
    def _truncate_text_file(content: str, file_path: str, max_tokens: int) -> str:
        """
        Truncate a text file while preserving important sections like headings.
        
        Args:
            content: The file content to truncate.
            file_path: The path to the file.
            max_tokens: The maximum number of tokens to allow.
            
        Returns:
            The truncated file content.
        """
        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lstrip('.').lower() if ext else ""
        
        # Split the content into lines
        lines = content.split('\n')
        
        # For markdown files, prioritize headings and the content immediately after them
        if ext == "md":
            # Identify heading lines
            heading_indices = []
            for i, line in enumerate(lines):
                if re.match(r"^#+\s+.+", line):
                    heading_indices.append(i)
            
            # Include headings and a few lines after each heading
            important_line_indices = set()
            for i in heading_indices:
                important_line_indices.add(i)
                for j in range(1, 4):  # Include up to 3 lines after each heading
                    if i + j < len(lines):
                        important_line_indices.add(i + j)
            
            # Always include the first 20 lines
            for i in range(min(20, len(lines))):
                important_line_indices.add(i)
            
            # Sort the indices
            important_line_indices = sorted(list(important_line_indices))
            
            # Build the truncated content
            truncated_lines = []
            last_included = -2
            
            for i in important_line_indices:
                # If there's a gap, add a note
                if i > last_included + 1:
                    truncated_lines.append(f"[...{i - last_included - 1} lines truncated...]")
                
                # Add the important line
                truncated_lines.append(lines[i])
                last_included = i
            
            # If we didn't include the last line, add a note
            if last_included < len(lines) - 1:
                truncated_lines.append(f"[...{len(lines) - last_included - 1} lines truncated...]")
            
            # Join the lines
            truncated = '\n'.join(truncated_lines)
        else:
            # For regular text files, keep the beginning and end
            head_size = int(max_tokens * 0.7)  # 70% of tokens for the beginning
            tail_size = max_tokens - head_size  # Remaining tokens for the end
            
            # Convert token limits to character limits (rough approximation)
            head_chars = int(head_size * 4 / TOKEN_MULTIPLIERS.get(ext, 0.7))
            tail_chars = int(tail_size * 4 / TOKEN_MULTIPLIERS.get(ext, 0.7))
            
            # Get the head and tail sections
            head = content[:head_chars]
            tail = content[-tail_chars:] if len(content) > head_chars + tail_chars else ""
            
            # Combine with a note about truncation
            if tail:
                truncated = f"{head}\n\n[...text truncated...]\n\n{tail}"
            else:
                truncated = head
        
        # If still too large, fall back to aggressive truncation
        if ContextOptimizer.estimate_tokens(truncated, ext) > max_tokens:
            return ContextOptimizer._aggressive_truncate(content, file_path, max_tokens)
        
        return truncated
    
    @staticmethod
    def _truncate_data_file(content: str, file_path: str, max_tokens: int) -> str:
        """
        Truncate a data file (JSON, YAML) while preserving structure.
        
        Args:
            content: The file content to truncate.
            file_path: The path to the file.
            max_tokens: The maximum number of tokens to allow.
            
        Returns:
            The truncated file content.
        """
        # For data files, we'll keep the structure but truncate array contents
        # This is a simplified approach - a more sophisticated one would parse the JSON/YAML
        
        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lstrip('.').lower() if ext else ""
        
        # Split the content into lines
        lines = content.split('\n')
        
        # Keep track of array contexts
        in_array = False
        array_items = 0
        max_array_items = 5  # Maximum number of array items to keep
        
        # Process each line
        truncated_lines = []
        for line in lines:
            # Check if we're entering or leaving an array
            if re.search(r'[\[\{]$', line):
                in_array = True
                array_items = 0
                truncated_lines.append(line)
            elif re.search(r'^[\]\}]', line):
                in_array = False
                truncated_lines.append(line)
            elif in_array:
                # If we're in an array, only keep a limited number of items
                if array_items < max_array_items:
                    truncated_lines.append(line)
                    array_items += 1
                elif array_items == max_array_items:
                    truncated_lines.append("  [...additional items truncated...]")
                    array_items += 1
            else:
                # Not in an array, keep the line
                truncated_lines.append(line)
        
        # Join the lines
        truncated = '\n'.join(truncated_lines)
        
        # If still too large, fall back to aggressive truncation
        if ContextOptimizer.estimate_tokens(truncated, ext) > max_tokens:
            return ContextOptimizer._aggressive_truncate(content, file_path, max_tokens)
        
        return truncated
    
    @staticmethod
    def _default_truncate(content: str, file_path: str, max_tokens: int) -> str:
        """
        Default truncation strategy for unknown file types.
        
        Args:
            content: The file content to truncate.
            file_path: The path to the file.
            max_tokens: The maximum number of tokens to allow.
            
        Returns:
            The truncated file content.
        """
        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lstrip('.').lower() if ext else ""
        
        # For unknown file types, keep the beginning and end
        head_size = int(max_tokens * 0.7)  # 70% of tokens for the beginning
        tail_size = max_tokens - head_size  # Remaining tokens for the end
        
        # Convert token limits to character limits (rough approximation)
        head_chars = int(head_size * 4 / TOKEN_MULTIPLIERS.get("default", 0.9))
        tail_chars = int(tail_size * 4 / TOKEN_MULTIPLIERS.get("default", 0.9))
        
        # Get the head and tail sections
        head = content[:head_chars]
        tail = content[-tail_chars:] if len(content) > head_chars + tail_chars else ""
        
        # Combine with a note about truncation
        if tail:
            truncated = f"{head}\n\n[...content truncated...]\n\n{tail}"
        else:
            truncated = head
        
        return truncated
    
    @staticmethod
    def optimize_context(context: Dict[str, Any], issue_explanation: str, max_tokens: int = MAX_TOTAL_TOKENS) -> Dict[str, Any]:
        """
        Optimize the context for LLM token usage by prioritizing relevant files and truncating content.
        
        Args:
            context: The context dictionary containing project files.
            issue_explanation: The user's issue explanation.
            max_tokens: The maximum number of tokens to allow for the entire context.
            
        Returns:
            The optimized context dictionary.
        """
        if not context or "project_files_context" not in context:
            return context
            
        # Extract the files from the context
        files_context = context["project_files_context"]
        if not files_context:
            return context
            
        # Estimate current token usage
        current_tokens = 0
        file_tokens = {}
        
        for file_path, content in files_context.items():
            _, ext = os.path.splitext(file_path)
            ext = ext.lstrip('.').lower() if ext else ""
            tokens = ContextOptimizer.estimate_tokens(content, ext)
            file_tokens[file_path] = tokens
            current_tokens += tokens
            
        logger.info(f"Current context size: {current_tokens} tokens")
        
        # If already within limits, no need to optimize
        if current_tokens <= max_tokens:
            return context
            
        # Prioritize files based on relevance to the issue
        prioritized_files = ContextOptimizer._prioritize_files(files_context, issue_explanation)
        
        # Optimize the context by truncating or removing files
        optimized_files = {}
        remaining_tokens = max_tokens
        
        for file_path in prioritized_files:
            content = files_context[file_path]
            tokens = file_tokens[file_path]
            
            # If this file would fit within the remaining tokens, include it
            if tokens <= remaining_tokens:
                optimized_files[file_path] = content
                remaining_tokens -= tokens
            else:
                # Try to truncate the file to fit
                truncated = ContextOptimizer.truncate_file_content(content, file_path, min(MAX_TOKENS_PER_FILE, remaining_tokens))
                truncated_tokens = ContextOptimizer.estimate_tokens(truncated, os.path.splitext(file_path)[1].lstrip('.').lower())
                
                if truncated_tokens <= remaining_tokens:
                    optimized_files[file_path] = truncated
                    remaining_tokens -= truncated_tokens
                # Otherwise, skip this file
        
        # Update the context with the optimized files
        optimized_context = context.copy()
        optimized_context["project_files_context"] = optimized_files
        
        # Log the optimization results
        logger.info(f"Optimized context size: {max_tokens - remaining_tokens} tokens")
        logger.info(f"Included {len(optimized_files)} of {len(files_context)} files")
        
        return optimized_context
    
    @staticmethod
    def _prioritize_files(files_context: Dict[str, str], issue_explanation: str) -> List[str]:
        """
        Prioritize files based on relevance to the issue.
        
        Args:
            files_context: Dictionary of file paths to content.
            issue_explanation: The user's issue explanation.
            
        Returns:
            List of file paths sorted by priority (highest first).
        """
        # Extract keywords from the issue explanation
        keywords = set(re.findall(r'\b\w+\b', issue_explanation.lower()))
        
        # Remove common stop words
        stop_words = {"a", "an", "the", "and", "or", "but", "in", "on", "at", "to", "for", "with", "by", "about", "as"}
        keywords = keywords - stop_words
        
        # Score each file based on relevance
        file_scores = {}
        
        for file_path, content in files_context.items():
            score = 0
            
            # Prioritize based on file extension
            _, ext = os.path.splitext(file_path)
            ext = ext.lstrip('.').lower() if ext else ""
            
            # Higher score for code files
            if ext in ["py", "js", "ts", "go", "java", "rb", "php", "cs", "cpp", "c", "rs"]:
                score += 10
            # Medium score for configuration and documentation
            elif ext in ["md", "json", "yaml", "yml", "toml", "ini", "cfg"]:
                score += 5
            
            # Prioritize based on file path
            if "main" in file_path.lower():
                score += 5
            if "config" in file_path.lower():
                score += 3
            if "test" in file_path.lower():
                score -= 2  # Lower priority for test files
            
            # Prioritize based on keyword matches
            file_keywords = set(re.findall(r'\b\w+\b', content.lower()[:1000]))  # Only check the first 1000 chars for efficiency
            matches = len(keywords.intersection(file_keywords))
            score += matches * 2
            
            # Store the score
            file_scores[file_path] = score
        
        # Sort files by score (highest first)
        return sorted(files_context.keys(), key=lambda x: file_scores[x], reverse=True)
