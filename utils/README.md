# Workflow Utilities

This directory contains utility modules for the Workflow system.

## Context Optimizer

The `ContextOptimizer` class in `context_optimizer.py` is responsible for optimizing context for LLM token usage. It provides methods for:

- Estimating token usage for different file types
- Truncating file content to fit within token limits while preserving important sections
- Prioritizing files based on relevance to the user's issue
- Optimizing the entire context to stay within token limits

### Usage

```python
from utils.context_optimizer import ContextOptimizer

# Estimate tokens for a string
tokens = ContextOptimizer.estimate_tokens("Hello world", "py")

# Truncate file content
truncated = ContextOptimizer.truncate_file_content(content, file_path, max_tokens=1500)

# Optimize the entire context
optimized_context = ContextOptimizer.optimize_context(context, issue_explanation, max_tokens=6000)
```

## File Cache

The `FileCache` class in `file_cache.py` provides a simple file-based caching mechanism for repository content. It helps reduce the number of API calls to Gitea and improves performance by:

- Caching file content in a local directory
- Using SHA-based validation to avoid re-fetching unchanged files
- Providing automatic cache expiration based on configurable TTL
- Supporting cache invalidation for specific repositories, branches, or files

### Usage

```python
from utils.file_cache import FileCache

# Initialize the cache
cache = FileCache(cache_dir="/path/to/cache", max_age_seconds=3600)

# Get a file from the cache
content = cache.get("owner", "repo", "file_path", "branch")

# Put a file in the cache
cache.put("owner", "repo", "file_path", "branch", "file_content", "sha")

# Invalidate cache entries
cache.invalidate("owner", "repo")  # Invalidate all files in a repository
cache.invalidate("owner", "repo", "file_path")  # Invalidate a specific file
cache.invalidate("owner", "repo", file_path=None, ref="branch")  # Invalidate all files in a branch

# Clear the entire cache
cache.clear()

# Get cache statistics
stats = cache.get_stats()
print(f"Cache contains {stats['entries']} entries, total size: {stats['total_size_mb']:.2f} MB")
```

### Configuration

The `FileCache` class can be configured using environment variables:

- `FILE_CACHE_DIR`: The directory to store the cache files. If not set, defaults to `~/.workflow_cache`.
- `FILE_CACHE_MAX_AGE`: The maximum age of cache entries in seconds. If not set, defaults to 3600 (1 hour).

## Integration with Gitea-MCP Connector

The `GiteaMCPConnector` class in `2_giteamcp_connector.py` has been updated to use the `FileCache` class for all file content retrieval. This integration:

1. Checks the cache before making API calls to Gitea
2. Stores file content in the cache after retrieval
3. Uses SHA-based validation to avoid re-fetching unchanged files
4. Provides detailed logging for cache hits and misses

This integration significantly improves performance by reducing the number of API calls to Gitea and avoiding re-fetching unchanged files.
