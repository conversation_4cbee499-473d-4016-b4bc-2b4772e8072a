#!/usr/bin/env python3
"""
File caching utility for the workflow system.
"""

import os
import json
import time
import logging
import hashlib
from typing import Dict, Any, Optional, List, Tuple, Set

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("FileCache")

class FileCache:
    """
    A simple file-based cache for repository content.
    
    This cache stores file content from Gitea repositories to avoid re-fetching
    the same content multiple times. It uses a simple file-based approach with
    a JSON index file and separate files for the cached content.
    """
    
    def __init__(self, cache_dir: str = None, max_age_seconds: int = 3600):
        """
        Initialize the file cache.
        
        Args:
            cache_dir: The directory to store the cache files. If None, uses a default directory.
            max_age_seconds: The maximum age of cache entries in seconds. Default is 1 hour.
        """
        # Set the cache directory
        if cache_dir is None:
            self.cache_dir = os.path.join(os.path.expanduser("~"), ".workflow_cache")
        else:
            self.cache_dir = cache_dir
            
        # Create the cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Set the maximum age of cache entries
        self.max_age_seconds = max_age_seconds
        
        # Load the cache index
        self.index_file = os.path.join(self.cache_dir, "index.json")
        self.index = self._load_index()
        
        logger.info(f"Initialized file cache in {self.cache_dir}")
        logger.info(f"Cache contains {len(self.index)} entries")

    def _load_index(self) -> Dict[str, Dict[str, Any]]:
        """
        Load the cache index from the index file.
        
        Returns:
            The cache index as a dictionary.
        """
        if os.path.exists(self.index_file):
            try:
                with open(self.index_file, "r") as f:
                    index = json.load(f)
                    
                # Filter out expired entries
                current_time = time.time()
                valid_entries = {}
                for key, entry in index.items():
                    if current_time - entry["timestamp"] <= self.max_age_seconds:
                        valid_entries[key] = entry
                    else:
                        # Remove the cached file
                        cache_file = os.path.join(self.cache_dir, entry["file_name"])
                        if os.path.exists(cache_file):
                            os.remove(cache_file)
                            
                # Save the filtered index
                if len(valid_entries) != len(index):
                    with open(self.index_file, "w") as f:
                        json.dump(valid_entries, f, indent=2)
                        
                return valid_entries
            except Exception as e:
                logger.error(f"Error loading cache index: {str(e)}")
                return {}
        else:
            return {}

    def _save_index(self):
        """Save the cache index to the index file."""
        try:
            with open(self.index_file, "w") as f:
                json.dump(self.index, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving cache index: {str(e)}")

    def _generate_key(self, owner: str, repo: str, file_path: str, ref: str) -> str:
        """
        Generate a cache key for a file.
        
        Args:
            owner: The owner of the repository.
            repo: The name of the repository.
            file_path: The path to the file.
            ref: The branch or commit reference.
            
        Returns:
            A cache key as a string.
        """
        # Create a string with all the parameters
        key_string = f"{owner}/{repo}/{file_path}/{ref}"
        
        # Hash the string to create a unique key
        return hashlib.md5(key_string.encode()).hexdigest()

    def get(self, owner: str, repo: str, file_path: str, ref: str) -> Optional[str]:
        """
        Get a file from the cache.
        
        Args:
            owner: The owner of the repository.
            repo: The name of the repository.
            file_path: The path to the file.
            ref: The branch or commit reference.
            
        Returns:
            The file content as a string, or None if the file is not in the cache.
        """
        # Generate the cache key
        key = self._generate_key(owner, repo, file_path, ref)
        
        # Check if the key is in the index
        if key in self.index:
            entry = self.index[key]
            
            # Check if the entry is expired
            if time.time() - entry["timestamp"] > self.max_age_seconds:
                # Remove the entry from the index
                del self.index[key]
                self._save_index()
                
                # Remove the cached file
                cache_file = os.path.join(self.cache_dir, entry["file_name"])
                if os.path.exists(cache_file):
                    os.remove(cache_file)
                    
                return None
            
            # Get the cached file
            cache_file = os.path.join(self.cache_dir, entry["file_name"])
            if os.path.exists(cache_file):
                try:
                    with open(cache_file, "r") as f:
                        content = f.read()
                    
                    logger.debug(f"Cache hit for {owner}/{repo}/{file_path}@{ref}")
                    return content
                except Exception as e:
                    logger.error(f"Error reading cached file: {str(e)}")
                    return None
            else:
                # The cached file doesn't exist, remove the entry from the index
                del self.index[key]
                self._save_index()
                return None
        else:
            return None

    def put(self, owner: str, repo: str, file_path: str, ref: str, content: str, sha: str = None) -> bool:
        """
        Put a file in the cache.
        
        Args:
            owner: The owner of the repository.
            repo: The name of the repository.
            file_path: The path to the file.
            ref: The branch or commit reference.
            content: The file content.
            sha: The SHA of the file. If provided, it will be used to check if the file has changed.
            
        Returns:
            True if the file was successfully cached, False otherwise.
        """
        # Generate the cache key
        key = self._generate_key(owner, repo, file_path, ref)
        
        # Check if the key is already in the index and the SHA matches
        if key in self.index and sha is not None and self.index[key].get("sha") == sha:
            # Update the timestamp
            self.index[key]["timestamp"] = time.time()
            self._save_index()
            
            logger.debug(f"Cache entry updated for {owner}/{repo}/{file_path}@{ref}")
            return True
        
        # Generate a file name for the cached file
        file_name = f"{key}.txt"
        cache_file = os.path.join(self.cache_dir, file_name)
        
        # Write the content to the cached file
        try:
            with open(cache_file, "w") as f:
                f.write(content)
                
            # Add the entry to the index
            self.index[key] = {
                "owner": owner,
                "repo": repo,
                "file_path": file_path,
                "ref": ref,
                "file_name": file_name,
                "timestamp": time.time(),
                "sha": sha
            }
            self._save_index()
            
            logger.debug(f"Cache entry created for {owner}/{repo}/{file_path}@{ref}")
            return True
        except Exception as e:
            logger.error(f"Error caching file: {str(e)}")
            return False

    def invalidate(self, owner: str, repo: str, file_path: str = None, ref: str = None):
        """
        Invalidate cache entries.
        
        Args:
            owner: The owner of the repository.
            repo: The name of the repository.
            file_path: The path to the file. If None, invalidates all files in the repository.
            ref: The branch or commit reference. If None, invalidates all references.
        """
        # Find keys to invalidate
        keys_to_invalidate = []
        for key, entry in self.index.items():
            if entry["owner"] == owner and entry["repo"] == repo:
                if file_path is None or entry["file_path"] == file_path:
                    if ref is None or entry["ref"] == ref:
                        keys_to_invalidate.append(key)
        
        # Invalidate the keys
        for key in keys_to_invalidate:
            entry = self.index[key]
            
            # Remove the cached file
            cache_file = os.path.join(self.cache_dir, entry["file_name"])
            if os.path.exists(cache_file):
                os.remove(cache_file)
                
            # Remove the entry from the index
            del self.index[key]
            
        # Save the index
        if keys_to_invalidate:
            self._save_index()
            logger.info(f"Invalidated {len(keys_to_invalidate)} cache entries for {owner}/{repo}")

    def clear(self):
        """Clear the entire cache."""
        # Remove all cached files
        for entry in self.index.values():
            cache_file = os.path.join(self.cache_dir, entry["file_name"])
            if os.path.exists(cache_file):
                os.remove(cache_file)
                
        # Clear the index
        self.index = {}
        self._save_index()
        
        logger.info("Cache cleared")

    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the cache.
        
        Returns:
            A dictionary with cache statistics.
        """
        # Count the number of entries by repository
        repos = {}
        for entry in self.index.values():
            repo_key = f"{entry['owner']}/{entry['repo']}"
            if repo_key not in repos:
                repos[repo_key] = 0
            repos[repo_key] += 1
            
        # Calculate the total size of the cache
        total_size = 0
        for entry in self.index.values():
            cache_file = os.path.join(self.cache_dir, entry["file_name"])
            if os.path.exists(cache_file):
                total_size += os.path.getsize(cache_file)
                
        return {
            "entries": len(self.index),
            "repositories": repos,
            "total_size_bytes": total_size,
            "total_size_mb": total_size / (1024 * 1024)
        }
