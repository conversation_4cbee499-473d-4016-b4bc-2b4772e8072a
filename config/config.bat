@echo off
REM Workflow Configuration Template
REM Copy this file to config\local_config.bat and edit with your actual values
REM DO NOT commit local_config.bat to version control

REM Gitea Configuration
if not defined GITEA_URL set GITEA_URL=http://localhost:3000
if not defined GITEA_ACCESS_TOKEN set GITEA_ACCESS_TOKEN=your_gitea_token_here

REM MCP Server Configuration
if not defined GITMCP_SERVER_URL set GITMCP_SERVER_URL=http://localhost:8080

REM OpenAI-Compatible Server Configuration
if not defined OPENAI_API_BASE_URL set OPENAI_API_BASE_URL=http://localhost:8001/v1
if not defined OPENAI_API_KEY set OPENAI_API_KEY=

REM Local Repository Clone Path
if not defined LOCAL_REPO_CLONE_PATH set LOCAL_REPO_CLONE_PATH=%TEMP%\gitea_clones

REM File Cache Configuration
if not defined FILE_CACHE_DIR set FILE_CACHE_DIR=%USERPROFILE%\.workflow_cache
if not defined FILE_CACHE_MAX_AGE set FILE_CACHE_MAX_AGE=3600

echo ✅ Workflow environment variables set.
echo 🔗 GITEA_URL=%GITEA_URL%
echo 🔗 GITMCP_SERVER_URL=%GITMCP_SERVER_URL%
echo 🔗 OPENAI_API_BASE_URL=%OPENAI_API_BASE_URL%
echo 📁 LOCAL_REPO_CLONE_PATH=%LOCAL_REPO_CLONE_PATH%
echo.
echo ⚠️  IMPORTANT: Edit config\local_config.bat with your actual credentials
echo ⚠️  DO NOT commit local_config.bat to version control
