# Workflow Environment Configuration Example
# Copy this file to .env and fill in your actual values
# DO NOT commit .env to version control

# Gitea Configuration
GITEA_URL=http://localhost:3000
GITEA_ACCESS_TOKEN=your_gitea_personal_access_token_here
GITEA_USERNAME=your_gitea_username
GITEA_PASSWORD=your_gitea_password

# MCP Server Configuration
GITMCP_SERVER_URL=http://localhost:8080
GITMCP_API_KEY=your_mcp_api_key_if_required

# OpenAI-Compatible Server Configuration
OPENAI_API_BASE_URL=http://localhost:8001/v1
OPENAI_API_KEY=your_openai_api_key_if_required

# Local Repository Clone Path
# Windows: C:\temp\gitea_clones
# Linux/Mac: /tmp/gitea_clones
LOCAL_REPO_CLONE_PATH=/tmp/gitea_clones

# File Cache Configuration
FILE_CACHE_DIR=~/.workflow_cache
FILE_CACHE_MAX_AGE=3600

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=workflow.log

# Security Settings
VALIDATE_SSL=true
REQUEST_TIMEOUT=30
MAX_FILE_SIZE_MB=10
